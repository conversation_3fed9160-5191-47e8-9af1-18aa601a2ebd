# 软件监控脚本设置说明

## 功能说明
这个脚本可以：
- 开机自动启动
- 每10秒检查你的软件是否在运行
- 如果软件没有运行，自动启动软件
- 记录详细的运行日志

## 使用步骤

### 第一步：配置脚本（两种方法）

#### 方法一：使用配置向导（推荐，适合绿色软件）
1. 右键点击 `配置向导.ps1`，选择"使用PowerShell运行"
2. 在弹出的文件选择对话框中，选择你的软件exe文件
3. 按照提示设置检查间隔（默认10秒）
4. 配置向导会自动生成正确的配置

#### 方法二：手动修改配置
打开 `软件监控脚本.ps1` 文件，修改以下配置：

```powershell
$ProcessName = "MyApp"             # 改为你的软件进程名（不包含.exe）
$ExecutablePath = "D:\MyApp\MyApp.exe"  # 改为你的软件完整路径
$WorkingDirectory = "D:\MyApp"           # 改为软件所在文件夹
$StartupArguments = ""             # 绿色软件通常不需要启动参数
```

**对于绿色软件（免安装软件）：**
- 进程名就是exe文件名（不包含.exe）
- 工作目录就是软件解压的文件夹
- 通常不需要启动参数

### 第二步：测试脚本
1. 右键点击 `启动监控脚本.bat`，选择"以管理员身份运行"
2. 观察是否能正常启动和监控你的软件
3. 检查日志文件 `C:\Logs\AppMonitor.log`

### 第三步：设置开机自启
有两种方法设置开机自启：

#### 方法一：使用任务计划程序（推荐）
1. 按 `Win + R`，输入 `taskschd.msc`，回车
2. 在右侧点击"创建基本任务"
3. 任务名称：软件监控脚本
4. 触发器：选择"计算机启动时"
5. 操作：选择"启动程序"
6. 程序或脚本：填写 `启动监控脚本.bat` 的完整路径
7. 起始于：填写脚本所在的文件夹路径
8. 完成创建

#### 方法二：添加到启动文件夹
1. 按 `Win + R`，输入 `shell:startup`，回车
2. 将 `启动监控脚本.bat` 复制到这个文件夹

### 第四步：高级设置（可选）

#### 设置为Windows服务（更稳定）
如果你需要更稳定的运行，可以使用NSSM将脚本设置为Windows服务：

1. 下载NSSM：https://nssm.cc/download
2. 解压到某个目录，比如 `C:\nssm`
3. 以管理员身份打开命令提示符
4. 运行以下命令：
```cmd
C:\nssm\nssm.exe install 软件监控服务
```
5. 在弹出的窗口中：
   - Path: 选择 `powershell.exe`
   - Arguments: `-WindowStyle Hidden -ExecutionPolicy Bypass -File "你的脚本完整路径\软件监控脚本.ps1"`
6. 点击"Install service"

#### 修改检查间隔
在脚本中修改这一行：
```powershell
$CheckInterval = 10    # 改为你想要的秒数
```

## 日志文件
- 日志文件位置：`C:\Logs\AppMonitor.log`
- 记录内容：启动时间、检查结果、错误信息等
- 可以通过查看日志了解脚本运行状态

## 故障排除

### 脚本无法运行
1. 确保以管理员身份运行
2. 检查PowerShell执行策略：
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

### 软件无法启动
1. 检查软件路径是否正确
2. 检查软件是否需要特殊权限
3. 查看日志文件中的错误信息

### 进程检测不准确
1. 确认进程名是否正确（不包含.exe）
2. 有些软件可能有多个进程，选择主进程名

## 注意事项
1. 建议先测试脚本是否正常工作，再设置开机自启
2. 如果软件需要用户交互，可能需要调整脚本
3. 定期检查日志文件，确保脚本正常运行
4. 如果要停止监控，可以在任务管理器中结束PowerShell进程
