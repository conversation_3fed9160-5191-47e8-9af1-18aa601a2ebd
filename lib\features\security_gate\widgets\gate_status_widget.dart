import 'package:a3g/core/utils/window_util.dart';
import 'package:flutter/material.dart';
import 'package:base_package/base_package.dart';

import '../models/gate_state.dart';

/// 闸机状态显示组件
class GateStatusWidget extends StatelessWidget {
  final GateState state;
  final String message;
  final Animation<double>? pulseAnimation;
  
  const GateStatusWidget({
    Key? key,
    required this.state,
    required this.message,
    this.pulseAnimation,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(40.p),
      margin: EdgeInsets.symmetric(horizontal: 60.p),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(30.p),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 20,
            offset: Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 状态图标
          _buildStateIcon(),
          
          SizedBox(height: 30.p),
          
          // 状态文字
          Text(
            message,
            style: TextStyle(
              color: Colors.white,
              fontSize: 36.p,
              fontWeight: FontWeight.w500,
              height: 1.2,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          
          SizedBox(height: 20.p),
          
          // 状态指示器
          _buildStateIndicator(),
        ],
      ),
    );
  }
  
  /// 构建状态图标
  Widget _buildStateIcon() {
    Widget iconWidget = Icon(
      state.icon,
      size: 80.p,
      color: state.indicatorColor,
    );
    
    // 如果有脉冲动画且状态为活跃状态，应用动画
    if (pulseAnimation != null && state.isActive) {
      return AnimatedBuilder(
        animation: pulseAnimation!,
        builder: (context, child) {
          return Transform.scale(
            scale: pulseAnimation!.value,
            child: iconWidget,
          );
        },
      );
    }
    
    return iconWidget;
  }
  
  /// 构建状态指示器
  Widget _buildStateIndicator() {
    return Container(
      width: 300.p,
      height: 6.p,
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.3),
        borderRadius: BorderRadius.circular(3.p),
      ),
      child: Stack(
        children: [
          // 背景条
          Container(
            width: double.infinity,
            height: double.infinity,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(3.p),
            ),
          ),
          
          // 进度条
          AnimatedContainer(
            duration: Duration(milliseconds: 500),
            width: _getIndicatorWidth(),
            height: double.infinity,
            decoration: BoxDecoration(
              color: state.indicatorColor,
              borderRadius: BorderRadius.circular(3.p),
              boxShadow: [
                BoxShadow(
                  color: state.indicatorColor.withOpacity(0.5),
                  blurRadius: 8,
                  spreadRadius: 1,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  /// 获取指示器宽度
  double _getIndicatorWidth() {
    switch (state) {
      case GateState.idle:
        return 60.p;
      case GateState.enterStarted:
      case GateState.exitStarted:
        return 120.p;
      case GateState.enterScanning:
      case GateState.exitScanning:
        return 180.p;
      case GateState.enterOpening:
      case GateState.exitChecking:
        return 240.p;
      case GateState.enterOver:
      case GateState.exitOver:
        return 300.p;
      case GateState.error:
        return 300.p;
    }
  }
}

/// 简化版状态指示器
class SimpleGateStatusIndicator extends StatelessWidget {
  final GateState state;
  final double size;
  
  const SimpleGateStatusIndicator({
    Key? key,
    required this.state,
    this.size = 16.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: state.indicatorColor,
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: state.indicatorColor.withOpacity(0.5),
            blurRadius: 4,
            spreadRadius: 1,
          ),
        ],
      ),
      child: state.isActive
          ? Container(
              margin: EdgeInsets.all(size * 0.2),
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
              ),
            )
          : null,
    );
  }
}

/// 状态文字显示组件
class GateStatusText extends StatelessWidget {
  final GateState state;
  final String? customMessage;
  final TextStyle? textStyle;
  
  const GateStatusText({
    Key? key,
    required this.state,
    this.customMessage,
    this.textStyle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final message = customMessage ?? state.displayName;
    
    return Text(
      message,
      style: textStyle ?? TextStyle(
        color: state.indicatorColor,
        fontSize: 18.p,
        fontWeight: FontWeight.w500,
      ),
      textAlign: TextAlign.center,
    );
  }
}
