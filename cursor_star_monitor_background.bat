@echo off
REM Background monitor script - runs without visible window

REM ==================== Configuration ====================
set "PROCESS_NAME=cursor_star.exe"
set "EXE_PATH=C:\Users\<USER>\Desktop\Release\cursor_star.exe"
set "CHECK_INTERVAL=10"
set "LOG_FILE=%~dp0monitor.log"
REM ==================== Configuration End ====================

REM Create log entry
echo [%date% %time%] cursor_star Monitor Started >> "%LOG_FILE%"

REM Check if executable exists
if not exist "%EXE_PATH%" (
    echo [%date% %time%] ERROR: Cannot find executable file %EXE_PATH% >> "%LOG_FILE%"
    exit /b 1
)

:MONITOR_LOOP
    REM Check if process is running
    tasklist /FI "IMAGENAME eq %PROCESS_NAME%" 2>NUL | find /I "%PROCESS_NAME%" >NUL
    
    if %ERRORLEVEL% EQU 0 (
        REM Process is running - log every 60 seconds to avoid spam
        set /a "counter+=1"
        if !counter! GEQ 6 (
            echo [%date% %time%] Software is running >> "%LOG_FILE%"
            set "counter=0"
        )
    ) else (
        echo [%date% %time%] Software not running, starting... >> "%LOG_FILE%"
        start "" "%EXE_PATH%"
        if %ERRORLEVEL% EQU 0 (
            echo [%date% %time%] Software started successfully >> "%LOG_FILE%"
        ) else (
            echo [%date% %time%] Failed to start software >> "%LOG_FILE%"
        )
        set "counter=0"
    )
    
    REM Wait for specified seconds
    timeout /t %CHECK_INTERVAL% /nobreak >nul 2>&1
    
    goto MONITOR_LOOP
