@echo off
chcp 65001 >nul
title a3g软件监控

REM ==================== 配置区域 ====================
set "PROCESS_NAME=cursor_star.exe"
set "EXE_PATH=C:\Users\<USER>\Desktop\Release\cursor_star.exe"
set "CHECK_INTERVAL=10"
REM ==================== 配置区域结束 ====================

echo ==================== a3g软件监控启动 ====================
echo 监控进程: %PROCESS_NAME%
echo 软件路径: %EXE_PATH%
echo 检查间隔: %CHECK_INTERVAL% 秒
echo ========================================================

REM 检查软件文件是否存在
if not exist "%EXE_PATH%" (
    echo 错误: 找不到软件文件 %EXE_PATH%
    pause
    exit /b 1
)

echo 开始监控...
echo.

:MONITOR_LOOP
    REM 获取当前时间
    for /f "tokens=2 delims==" %%I in ('wmic OS Get localdatetime /value') do set "dt=%%I"
    set "current_time=%dt:~8,2%:%dt:~10,2%:%dt:~12,2%"
    
    REM 检查进程是否运行
    tasklist /FI "IMAGENAME eq %PROCESS_NAME%" 2>NUL | find /I "%PROCESS_NAME%" >NUL
    
    if %ERRORLEVEL% EQU 0 (
        echo [%current_time%] 软件正在运行
    ) else (
        echo [%current_time%] 软件未运行，正在启动...
        start "" "%EXE_PATH%"
        if %ERRORLEVEL% EQU 0 (
            echo [%current_time%] 软件启动成功
        ) else (
            echo [%current_time%] 软件启动失败
        )
    )
    
    REM 等待指定秒数
    timeout /t %CHECK_INTERVAL% /nobreak >nul
    
    goto MONITOR_LOOP
