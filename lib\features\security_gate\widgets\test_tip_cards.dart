import 'package:flutter/material.dart';
import 'package:a3g/core/utils/window_util.dart';
import 'tip_card.dart';
import 'radar_scan_tip_card.dart';

/// 测试TipCard组件的页面
class TestTipCardsPage extends StatelessWidget {
  const TestTipCardsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('测试TipCard组件'),
        backgroundColor: Colors.blue,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.blue.shade900,
              Colors.purple.shade900,
            ],
          ),
        ),
        child: Center(
          child: SingleChildScrollView(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(height: 20.p),

                // 测试认证成功的TipCard
                TipCard(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: Colors.green,
                        size: 60.p,
                      ),
                      SizedBox(height: 20.p),
                      Text(
                        '认证成功',
                        style: TextStyle(
                          color: Colors.green,
                          fontSize: 32.p,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 15.p),
                      Text(
                        '张某某，同学',
                        style: TextStyle(
                          color: const Color(0xFF242424),
                          fontSize: 28.p,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      SizedBox(height: 10.p),
                      Text(
                        '欢迎再次光临',
                        style: TextStyle(
                          color: const Color(0xFF242424),
                          fontSize: 20.p,
                          fontWeight: FontWeight.w400,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),

                SizedBox(height: 30.p),

                // 测试认证失败的TipCard
                TipCard(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error,
                        color: Colors.red,
                        size: 60.p,
                      ),
                      SizedBox(height: 20.p),
                      Text(
                        '认证失败',
                        style: TextStyle(
                          color: Colors.red,
                          fontSize: 32.p,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 15.p),
                      Text(
                        '未检测到借者信息，请先办理读者证',
                        style: TextStyle(
                          color: const Color(0xFF242424),
                          fontSize: 20.p,
                          fontWeight: FontWeight.w400,
                          height: 1.3,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),

                SizedBox(height: 30.p),

                // 测试RadarScanTipCard - 扫描中状态
                Column(
                  children: [
                    Text(
                      '扫描中状态演示',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 24.p,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 10.p),
                    RadarScanTipCard(
                      number: 2,
                      state: RadarScanState.scanning,
                    ),
                  ],
                ),

                SizedBox(height: 30.p),

                // 测试RadarScanTipCard - 检测到未借书籍状态
                Column(
                  children: [
                    Text(
                      '检测到未借书籍状态演示',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 24.p,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 10.p),
                    RadarScanTipCard(
                      number: 3,
                      state: RadarScanState.detected,
                    ),
                  ],
                ),

                SizedBox(height: 20.p),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
