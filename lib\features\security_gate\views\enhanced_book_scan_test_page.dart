import 'package:flutter/material.dart';
import 'dart:async';

import '../services/enhanced_rfid_service.dart';
import '../services/book_info_api_service.dart';
import '../models/book_info.dart';
import '../widgets/enhanced_gate_scanning_widget.dart';

/// 🔥 增强书籍扫描测试页面
/// 用于测试和演示增强的RFID扫描功能
class EnhancedBookScanTestPage extends StatefulWidget {
  @override
  _EnhancedBookScanTestPageState createState() => _EnhancedBookScanTestPageState();
}

class _EnhancedBookScanTestPageState extends State<EnhancedBookScanTestPage> {
  final EnhancedRFIDService _rfidService = EnhancedRFIDService.instance;
  final BookInfoApiService _bookInfoService = BookInfoApiService.instance;
  
  // 扫描状态
  bool _isScanning = false;
  bool _isInitialized = false;
  String? _statusMessage;
  String? _errorMessage;
  
  // 扫描结果
  List<String> _scannedBarcodes = [];
  Map<String, BookInfo> _booksInfo = {};
  
  // 事件订阅
  StreamSubscription? _barcodeSubscription;
  StreamSubscription? _bookResultSubscription;
  StreamSubscription? _countSubscription;
  StreamSubscription? _errorSubscription;
  
  @override
  void initState() {
    super.initState();
    _initializeServices();
  }
  
  @override
  void dispose() {
    _cancelSubscriptions();
    super.dispose();
  }
  
  /// 初始化服务
  Future<void> _initializeServices() async {
    try {
      setState(() {
        _statusMessage = '正在初始化服务...';
        _errorMessage = null;
      });
      
      // 初始化增强RFID服务
      await _rfidService.initialize();
      
      // 初始化书籍API服务
      _bookInfoService.initialize(
        baseUrl: 'http://localhost:3000', // 替换为实际API地址
      );
      
      // 设置事件监听
      _setupEventListeners();
      
      setState(() {
        _isInitialized = true;
        _statusMessage = '服务初始化完成';
      });
      
      print('增强书籍扫描测试页面初始化完成');
    } catch (e) {
      setState(() {
        _errorMessage = '初始化失败: $e';
        _statusMessage = null;
      });
      print('初始化失败: $e');
    }
  }
  
  /// 设置事件监听
  void _setupEventListeners() {
    // 监听条码流
    _barcodeSubscription = _rfidService.barcodeStream.listen(
      (barcode) {
        setState(() {
          if (!_scannedBarcodes.contains(barcode)) {
            _scannedBarcodes.add(barcode);
          }
        });
      },
    );
    
    // 监听书籍扫描结果流
    _bookResultSubscription = _rfidService.bookResultStream.listen(
      (result) {
        setState(() {
          if (result.bookInfo != null) {
            _booksInfo[result.barcode] = result.bookInfo!;
          }
        });
      },
    );
    
    // 监听计数流
    _countSubscription = _rfidService.countStream.listen(
      (count) {
        setState(() {
          _statusMessage = _isScanning ? '正在扫描... ($count)' : '扫描完成 ($count)';
        });
      },
    );
    
    // 监听错误流
    _errorSubscription = _rfidService.errorStream.listen(
      (error) {
        setState(() {
          _errorMessage = error;
        });
      },
    );
  }
  
  /// 取消事件订阅
  void _cancelSubscriptions() {
    _barcodeSubscription?.cancel();
    _bookResultSubscription?.cancel();
    _countSubscription?.cancel();
    _errorSubscription?.cancel();
  }
  
  /// 开始扫描
  Future<void> _startScanning() async {
    if (!_isInitialized || _isScanning) return;
    
    try {
      setState(() {
        _isScanning = true;
        _statusMessage = '正在启动扫描...';
        _errorMessage = null;
        _scannedBarcodes.clear();
        _booksInfo.clear();
      });
      
      await _rfidService.startEnhancedScanning();
      
      setState(() {
        _statusMessage = '扫描已启动';
      });
      
      print('增强RFID扫描已启动');
    } catch (e) {
      setState(() {
        _isScanning = false;
        _errorMessage = '启动扫描失败: $e';
      });
      print('启动扫描失败: $e');
    }
  }
  
  /// 停止扫描
  Future<void> _stopScanning() async {
    if (!_isScanning) return;
    
    try {
      setState(() {
        _statusMessage = '正在停止扫描...';
      });
      
      final result = await _rfidService.stopScanning();
      
      setState(() {
        _isScanning = false;
        _statusMessage = '扫描已停止，共扫描到 ${result.length} 个条码';
      });
      
      print('增强RFID扫描已停止，结果: ${result.length} 个条码');
    } catch (e) {
      setState(() {
        _isScanning = false;
        _errorMessage = '停止扫描失败: $e';
      });
      print('停止扫描失败: $e');
    }
  }
  
  /// 清空结果
  void _clearResults() {
    setState(() {
      _scannedBarcodes.clear();
      _booksInfo.clear();
      _errorMessage = null;
      _statusMessage = '结果已清空';
    });
    
    _rfidService.clearScanResult();
  }
  
  /// 手动添加测试条码
  void _addTestBarcode() {
    final testBarcodes = [
      'BOOK001',
      'BOOK002',
      'BOOK003',
      'BOOK004',
      'BOOK005',
    ];

    final barcode = testBarcodes[_scannedBarcodes.length % testBarcodes.length];

    setState(() {
      if (!_scannedBarcodes.contains(barcode)) {
        _scannedBarcodes.add(barcode);
      }
    });

    // 模拟获取书籍信息
    _bookInfoService.getBookInfo(barcode).then((bookInfo) {
      if (bookInfo != null) {
        setState(() {
          _booksInfo[barcode] = bookInfo;
        });
      }
    });
  }

  /// 批量添加测试条码
  void _addMultipleTestBarcodes() {
    final testBarcodes = [
      'BOOK101',
      'BOOK102',
      'BOOK103',
      'BOOK104',
      'BOOK105',
    ];

    setState(() {
      _statusMessage = '正在批量添加测试条码...';
    });

    for (String barcode in testBarcodes) {
      if (!_scannedBarcodes.contains(barcode)) {
        setState(() {
          _scannedBarcodes.add(barcode);
        });

        // 异步获取书籍信息
        _bookInfoService.getBookInfo(barcode).then((bookInfo) {
          if (bookInfo != null) {
            setState(() {
              _booksInfo[barcode] = bookInfo;
            });
          }
        });
      }
    }

    setState(() {
      _statusMessage = '批量添加完成，共添加 ${testBarcodes.length} 本测试书籍';
    });
  }

  /// 测试API连接
  Future<void> _testApiConnection() async {
    setState(() {
      _statusMessage = '正在测试API连接...';
      _errorMessage = null;
    });

    try {
      final isConnected = await _bookInfoService.testConnection();

      setState(() {
        if (isConnected) {
          _statusMessage = 'API连接测试成功 ✓';
        } else {
          _statusMessage = 'API连接测试失败，将使用模拟数据';
        }
      });

      // 显示详细状态
      final status = _bookInfoService.getStatus();
      print('API服务状态: $status');

    } catch (e) {
      setState(() {
        _errorMessage = 'API连接测试异常: $e';
        _statusMessage = null;
      });
      debugPrint('API连接测试异常: $e');
    }
  }

  /// 运行完整演示
  Future<void> _runFullDemo() async {
    if (_isScanning) {
      await _stopScanning();
      await Future.delayed(Duration(seconds: 1));
    }

    setState(() {
      _statusMessage = '开始运行完整演示...';
      _errorMessage = null;
      _scannedBarcodes.clear();
      _booksInfo.clear();
    });

    try {
      // 步骤1：测试API连接
      setState(() {
        _statusMessage = '步骤1/4: 测试API连接...';
      });
      await Future.delayed(Duration(seconds: 1));
      await _testApiConnection();

      // 步骤2：启动扫描
      setState(() {
        _statusMessage = '步骤2/4: 启动增强RFID扫描...';
      });
      await Future.delayed(Duration(seconds: 1));
      await _startScanning();

      // 步骤3：模拟扫描多本书籍
      setState(() {
        _statusMessage = '步骤3/4: 模拟扫描书籍...';
      });
      await Future.delayed(Duration(seconds: 2));

      final demoBarcodes = ['DEMO001', 'DEMO002', 'DEMO003', 'DEMO004'];
      for (int i = 0; i < demoBarcodes.length; i++) {
        final barcode = demoBarcodes[i];
        setState(() {
          _statusMessage = '步骤3/4: 扫描书籍 ${i + 1}/${demoBarcodes.length} - $barcode';
          if (!_scannedBarcodes.contains(barcode)) {
            _scannedBarcodes.add(barcode);
          }
        });

        // 获取书籍信息
        final bookInfo = await _bookInfoService.getBookInfo(barcode);
        if (bookInfo != null) {
          setState(() {
            _booksInfo[barcode] = bookInfo;
          });
        }

        await Future.delayed(Duration(milliseconds: 800));
      }

      // 步骤4：分析结果
      setState(() {
        _statusMessage = '步骤4/4: 分析扫描结果...';
      });
      await Future.delayed(Duration(seconds: 1));

      final totalBooks = _scannedBarcodes.length;
      final loadedBooks = _booksInfo.length;
      final borrowedBooks = _booksInfo.values.where((book) => book.isBorrowed).length;
      final unborrowedBooks = _booksInfo.values.where((book) => !book.isBorrowed).length;

      // 模拟开门决策
      final shouldOpen = unborrowedBooks == 0;

      setState(() {
        _statusMessage = '''演示完成!
扫描结果: $totalBooks 本书籍, $loadedBooks 本已加载信息
借阅状态: $borrowedBooks 本已借, $unborrowedBooks 本未借
开门决策: ${shouldOpen ? "✅ 允许通过" : "❌ 阻止出馆"}''';
      });

      // 停止扫描
      await Future.delayed(Duration(seconds: 2));
      await _stopScanning();

    } catch (e) {
      setState(() {
        _errorMessage = '演示运行失败: $e';
        _statusMessage = null;
      });
      debugPrint('演示运行失败: $e');
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('增强书籍扫描测试'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: Icon(Icons.info_outline),
            onPressed: _showServiceStatus,
            tooltip: '服务状态',
          ),
        ],
      ),
      body: Column(
        children: [
          // 控制面板
          _buildControlPanel(),
          
          // 状态显示
          _buildStatusPanel(),
          
          // 扫描结果显示
          Expanded(
            child: _buildScanResults(),
          ),
        ],
      ),
    );
  }
  
  /// 构建控制面板
  Widget _buildControlPanel() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Column(
        children: [
          // 第一行：主要控制按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton.icon(
                onPressed: _isInitialized && !_isScanning ? _startScanning : null,
                icon: Icon(Icons.play_arrow),
                label: Text('开始扫描'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  minimumSize: Size(120, 40),
                ),
              ),
              ElevatedButton.icon(
                onPressed: _isScanning ? _stopScanning : null,
                icon: Icon(Icons.stop),
                label: Text('停止扫描'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  minimumSize: Size(120, 40),
                ),
              ),
              ElevatedButton.icon(
                onPressed: _clearResults,
                icon: Icon(Icons.clear_all),
                label: Text('清空结果'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                  minimumSize: Size(120, 40),
                ),
              ),
            ],
          ),
          SizedBox(height: 12),
          // 第二行：测试和工具按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton.icon(
                onPressed: _addTestBarcode,
                icon: Icon(Icons.add),
                label: Text('添加测试条码'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  minimumSize: Size(140, 36),
                ),
              ),
              ElevatedButton.icon(
                onPressed: _addMultipleTestBarcodes,
                icon: Icon(Icons.library_books),
                label: Text('批量添加(5本)'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                  foregroundColor: Colors.white,
                  minimumSize: Size(140, 36),
                ),
              ),
              ElevatedButton.icon(
                onPressed: _testApiConnection,
                icon: Icon(Icons.wifi_tethering),
                label: Text('测试API连接'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.teal,
                  foregroundColor: Colors.white,
                  minimumSize: Size(140, 36),
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          // 第三行：演示按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton.icon(
                onPressed: _runFullDemo,
                icon: Icon(Icons.play_circle_filled),
                label: Text('运行完整演示'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.indigo,
                  foregroundColor: Colors.white,
                  minimumSize: Size(200, 36),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  /// 构建状态面板
  Widget _buildStatusPanel() {
    final totalBooks = _scannedBarcodes.length;
    final loadedBooks = _booksInfo.length;
    final borrowedBooks = _booksInfo.values.where((book) => book.isBorrowed).length;
    final unborrowedBooks = _booksInfo.values.where((book) => !book.isBorrowed).length;

    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _errorMessage != null ? Colors.red.shade50 : Colors.blue.shade50,
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Column(
        children: [
          // 主状态行
          Row(
            children: [
              Icon(
                _errorMessage != null ? Icons.error :
                _isScanning ? Icons.radar : Icons.check_circle,
                color: _errorMessage != null ? Colors.red :
                       _isScanning ? Colors.blue : Colors.green,
              ),
              SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _errorMessage ?? _statusMessage ?? '准备就绪',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: _errorMessage != null ? Colors.red : Colors.black87,
                      ),
                    ),
                    if (_isInitialized)
                      Text(
                        '服务状态: ${_isScanning ? "扫描中" : "就绪"} | 总计: $totalBooks 本书籍',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),

          // 统计信息行
          if (totalBooks > 0) ...[
            SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatChip('扫描', totalBooks.toString(), Colors.blue),
                _buildStatChip('已加载', loadedBooks.toString(), Colors.orange),
                _buildStatChip('已借', borrowedBooks.toString(), Colors.green),
                _buildStatChip('未借', unborrowedBooks.toString(), Colors.red),
                _buildStatChip('加载率', '${totalBooks > 0 ? (loadedBooks * 100 / totalBooks).toInt() : 0}%', Colors.purple),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// 构建统计芯片
  Widget _buildStatChip(String label, String value, Color color) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: color.withOpacity(0.8),
            ),
          ),
        ],
      ),
    );
  }
  
  /// 构建扫描结果
  Widget _buildScanResults() {
    if (!_isInitialized) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('正在初始化服务...'),
          ],
        ),
      );
    }
    
    return EnhancedGateScanningWidget(
      scannedBarcodes: _scannedBarcodes,
      booksInfo: _booksInfo,
      isScanning: _isScanning,
      statusMessage: _statusMessage,
    );
  }
  
  /// 显示服务状态
  void _showServiceStatus() {
    final rfidStatus = _rfidService.getStatus();
    final apiStatus = _bookInfoService.getStatus();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('服务状态'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('RFID服务状态:', style: TextStyle(fontWeight: FontWeight.bold)),
              ...rfidStatus.entries.map((entry) => 
                Text('${entry.key}: ${entry.value}')),
              SizedBox(height: 16),
              Text('API服务状态:', style: TextStyle(fontWeight: FontWeight.bold)),
              ...apiStatus.entries.map((entry) => 
                Text('${entry.key}: ${entry.value}')),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('关闭'),
          ),
        ],
      ),
    );
  }
}
