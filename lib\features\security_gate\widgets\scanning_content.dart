import 'dart:math';
import 'package:flutter/material.dart';
import 'package:a3g/core/utils/window_util.dart';
import '../models/silence_page_state.dart';
import 'radar_scan_tip_card.dart';

/// RFID扫描界面内容组件
class ScanningContent extends StatefulWidget {
  final UIContentData data;
  
  const ScanningContent({
    Key? key,
    required this.data,
  }) : super(key: key);

  @override
  State<ScanningContent> createState() => _ScanningContentState();
}

class _ScanningContentState extends State<ScanningContent>
    with TickerProviderStateMixin {
  
  late AnimationController _pulseController;
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
  }
  
  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: Duration(milliseconds: 1500),
      vsync: this,
    );
  }
  
  void _startAnimations() {
    _pulseController.repeat(reverse: true);
  }
  
  @override
  Widget build(BuildContext context) {
    final scannedCount = widget.data.scannedCount ?? 0;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 使用RadarScanTipCard显示扫描中状态
          RadarScanTipCard(
            number: scannedCount,
            state: RadarScanState.scanning, // 扫描中状态
          ),

          SizedBox(height: 40.p),

          // 扫描状态指示器
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(3, (index) {
              return AnimatedBuilder(
                animation: _pulseController,
                builder: (context, child) {
                  final delay = index * 0.3;
                  final animationValue = (_pulseController.value + delay) % 1.0;
                  final opacity = (sin(animationValue * 2 * pi) + 1) / 2;

                  return Container(
                    margin: EdgeInsets.symmetric(horizontal: 8.p),
                    width: 12.p,
                    height: 12.p,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.orange.withOpacity(opacity),
                    ),
                  );
                },
              );
            }),
          ),
        ],
      ),
    );
  }
  
  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }
}
