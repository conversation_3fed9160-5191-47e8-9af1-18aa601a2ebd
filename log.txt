2025-08-11 16:38:36.624371: 61
2025-08-11 16:38:36.847440: 开始初始化闸机协调器...
2025-08-11 16:38:37.029566: 从SettingProvider获取安全闸机配置: COM1 @ 9600
2025-08-11 16:38:37.029566: 安全闸机配置加载完成: COM1 @ 9600
2025-08-11 16:38:37.029566: 可用串口: [COM1, COM4, COM3]
2025-08-11 16:38:37.034552: 连接闸机串口: COM1
2025-08-11 16:38:37.034552: 尝试连接串口: COM1, 波特率: 9600
2025-08-11 16:38:37.034552: 串口连接成功: COM1 at 9600 baud
2025-08-11 16:38:37.034552: 开始监听串口数据
2025-08-11 16:38:37.035550: 串口连接状态变化: true
2025-08-11 16:38:37.035550: 闸机串口连接成功
2025-08-11 16:38:37.036546: 串口 COM1 连接成功 (波特率: 9600)
2025-08-11 16:38:37.036546: 闸机串口服务初始化成功: COM1
2025-08-11 16:38:37.036546: 开始监听串口数据（通过 GateSerialManager 事件流）
2025-08-11 16:38:37.036546: 开始监听闸机串口命令
2025-08-11 16:38:37.036546: 闸机协调器初始化完成
2025-08-11 16:38:37.036546: 安全闸机系统初始化完成
2025-08-11 16:38:37.037545: socket 连接成功,isBroadcast:false
2025-08-11 16:38:37.037545: changeSocketStatus:true
2025-08-11 16:38:37.037545: Sip2HeartBeatManager start loginACS:false askACS:false
2025-08-11 16:38:37.037545: Req msgType：Sip2MsgType.login ,length:72， ret:  9300CNhlsp_sip2|COsip2|CP3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AY1AZEC16
2025-08-11 16:38:37.045523: 开始初始化MultiAuthManager...
2025-08-11 16:38:37.045523: 多认证管理器状态变更: initializing
2025-08-11 16:38:37.045523: 认证优先级管理器: 开始加载认证方式
2025-08-11 16:38:37.045523: 配置的排序: [人脸识别认证, 微信扫码认证, 读者证认证, 身份证认证, 腾讯E证通认证, 手动输入认证, 社保卡认证, 市民卡认证, 借阅宝认证, 支付宝扫码认证, 芝麻信用码认证, 支付宝扫码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 电子社保卡认证, 拍照配置, 微信二维码认证, 上海随申码认证, 汇文二维码, 支付宝二维码认证, 支付宝二维码认证（阿里信用）]
2025-08-11 16:38:37.045523: 可用的认证方式: [读者证认证, 身份证认证, 腾讯E证通认证, 手动输入认证, 人脸识别认证, 社保卡认证, 市民卡认证, 微信二维码认证, 借阅宝认证, 支付宝扫码认证, 芝麻信用码认证, 支付宝扫码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 电子社保卡认证, 拍照配置, 上海随申码认证, 微信扫码认证, 汇文二维码]
2025-08-11 16:38:37.045523: 认证优先级管理器: 按配置顺序添加 微信扫码认证 -> 微信扫码
2025-08-11 16:38:37.045523: 认证优先级管理器: 按配置顺序添加 读者证认证 -> 读者证
2025-08-11 16:38:37.045523: 认证优先级管理器: 按配置顺序添加 社保卡认证 -> 社保卡
2025-08-11 16:38:37.045523: 认证优先级管理器: 按配置顺序添加 电子社保卡认证 -> 电子社保卡
2025-08-11 16:38:37.046521: 认证优先级管理器: 最终排序结果: 微信扫码 -> 读者证 -> 社保卡 -> 电子社保卡
2025-08-11 16:38:37.046521: 认证优先级管理器: 主要认证方式: 微信扫码
2025-08-11 16:38:37.046521: 多认证管理器: 从优先级管理器加载的认证方式: 微信扫码 -> 读者证 -> 社保卡 -> 电子社保卡
2025-08-11 16:38:37.046521: 多认证管理器: 当前默认显示方式: 微信扫码
2025-08-11 16:38:37.046521: 初始化二维码扫描认证服务
2025-08-11 16:38:37.046521: 初始化二维码扫码器
2025-08-11 16:38:37.046521: 二维码扫码器初始化完成
2025-08-11 16:38:37.046521: 二维码扫描认证服务初始化成功
2025-08-11 16:38:37.047518: 初始化共享二维码扫描认证服务
2025-08-11 16:38:37.047518: 微信扫码 认证服务初始化成功
2025-08-11 16:38:37.047518: 初始化读卡器认证服务
2025-08-11 16:38:37.047518: 读卡器认证服务初始化成功
2025-08-11 16:38:37.047518: 初始化共享读卡器认证服务
2025-08-11 16:38:37.047518: 读者证 认证服务初始化成功
2025-08-11 16:38:37.048516: 社保卡 认证服务初始化成功
2025-08-11 16:38:37.048516: 电子社保卡 认证服务初始化成功
2025-08-11 16:38:37.048516: 认证服务初始化完成，共初始化 4 种认证方式
2025-08-11 16:38:37.048516: 多认证管理器状态变更: idle
2025-08-11 16:38:37.048516: 多认证管理器初始化完成，启用的认证方式: [AuthMethod.wechatScanQRCode, AuthMethod.readerCard, AuthMethod.socialSecurityCard, AuthMethod.eletricSocialSecurityCard]
2025-08-11 16:38:37.048516: MultiAuthManager初始化完成
2025-08-11 16:38:37.048516: 开始初始化SilencePageViewModel...
2025-08-11 16:38:37.048516: 闸机串口服务已经初始化
2025-08-11 16:38:37.049512: 开始初始化闸机认证服务...
2025-08-11 16:38:37.049512: 闸机认证服务初始化完成，启用认证方式: 人脸识别、读者证、AuthMethod.wechatScanQRCode
2025-08-11 16:38:37.049512: 开始初始化增强RFID服务...
2025-08-11 16:38:37.049512: 开始初始化增强RFID服务...
2025-08-11 16:38:37.049512: SIP2图书信息服务初始化完成
2025-08-11 16:38:37.049512: 增强RFID服务初始化完成，配置了1个阅读器
2025-08-11 16:38:37.050514: 增强RFID服务初始化完成
2025-08-11 16:38:37.050514: SIP2图书信息服务初始化完成
2025-08-11 16:38:37.050514: 串口监听已经启动
2025-08-11 16:38:37.050514: SilencePageViewModel初始化完成
2025-08-11 16:38:37.093395: Rsp : 941AY1AZFDFC
2025-08-11 16:38:37.104365: loginRsp:{OK: 1, MsgSeqId: 1AZFDFC}
2025-08-11 16:38:37.104365: Sip2HeartBeatManager start loginACS:false askACS:true
2025-08-11 16:38:37.104365: 发送心跳
2025-08-11 16:38:37.104365: Req msgType：Sip2MsgType.scStatus ,length:20， ret:  9900522.00AY2AZFC9F
2025-08-11 16:38:37.127304: Rsp : 98YYYNNN00500320250811    1640212.00AOhlsp|AM海恒图书馆|BXYYYYYYYYYYYYYYYY|AN3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AF|AG|AY2AZD529
2025-08-11 16:38:37.227708: dispose IndexPage
2025-08-11 16:38:37.227708: IndexPage dispose
2025-08-11 16:38:40.081570: 收到串口命令: exit_start (出馆开始)
2025-08-11 16:38:40.081570: 处理出馆开始
2025-08-11 16:38:40.081570: 闸机状态变更: GateState.exitStarted
2025-08-11 16:38:40.081570: 页面状态变更: SilencePageState.rfidScanning
2025-08-11 16:38:40.081570: 开始增强RFID扫描
2025-08-11 16:38:40.081570: 开始增强RFID扫描
2025-08-11 16:38:40.081570: changeReaders
2025-08-11 16:38:40.081570: createIsolate isOpen:false,isOpening:false
2025-08-11 16:38:40.082568: createIsolate newport null
2025-08-11 16:38:40.583874: 找到网口配置: LSGate图书馆安全门RFID阅读器 - ************:6012
2025-08-11 16:38:40.583874: 使用网口连接: ************:6012
2025-08-11 16:38:40.583874: open():SendPort
2025-08-11 16:38:40.583874: untilDetcted():SendPort
2025-08-11 16:38:40.583874: 网口连接成功: ************:6012
2025-08-11 16:38:40.584871: startInventory():SendPort
2025-08-11 16:38:40.584871: 已添加标签数据监听器
2025-08-11 16:38:40.584871: 增强RFID扫描已启动
2025-08-11 16:38:40.584871: 增强RFID扫描已启动
2025-08-11 16:38:40.584871: 闸机状态变更: GateState.exitScanning
2025-08-11 16:38:40.584871: subThread :ReaderCommand.readerList
2025-08-11 16:38:40.585869: commandRsp:ReaderCommand.readerList
2025-08-11 16:38:40.585869: readerList：1,readerSetting：1
2025-08-11 16:38:40.585869: cacheUsedReaders:1
2025-08-11 16:38:40.585869: subThread :ReaderCommand.open
2025-08-11 16:38:40.585869: commandRsp:ReaderCommand.open
2025-08-11 16:38:40.585869: LSGate使用网络连接 IP: ************, Port: 6012, DeviceType: LSGControlCenter
2025-08-11 16:38:40.590857: LSGate device opened successfully, handle: 3047799166256
2025-08-11 16:38:40.591866: open reader readerType ：22 ret：0
2025-08-11 16:38:40.591866: [[22, 0]]
2025-08-11 16:38:40.591866: changeType:ReaderErrorType.openSuccess
2025-08-11 16:38:40.592850: subThread :ReaderCommand.untilDetected
2025-08-11 16:38:40.592850: commandRsp:ReaderCommand.untilDetected
2025-08-11 16:38:40.592850: subThread :ReaderCommand.startInventory
2025-08-11 16:38:40.592850: commandRsp:ReaderCommand.startInventory
2025-08-11 16:38:41.093444: 🧹 LSGate开始新的扫描流程，清空历史缓存...
2025-08-11 16:38:41.098431: 🔍 RDR_BuffMode_ClearRecords调用结果: ret=0
2025-08-11 16:38:41.200158: 🔧 LSGate使用缓冲模式获取RFID事件记录...
2025-08-11 16:38:41.230247: 🔍 RDR_BuffMode_FetchRecords调用结果: ret=0, hReader=3047799166256, flag=0
2025-08-11 16:38:41.230247: 📊 LSGate扫描结果: 发现 1 个标签报告
2025-08-11 16:38:41.230247: 📋 开始处理 1 个标签报告
2025-08-11 16:38:41.230247: 📋 获取第一个报告: hReport=3047799177920
2025-08-11 16:38:41.231245: 🔄 处理第 1/100 个报告, hReport=3047799177920
2025-08-11 16:38:41.231245: 📝 解析报告结果: parseRet=0, dataSize=58
2025-08-11 16:38:41.231245: 📊 原始数据 (58 bytes): 00 00 00 09 11 05 1f 05 09 e0 04 01 50 3c e8 63 8b 00 27 41 06 07 1c 30 c3 0c 75 02 01 b8 03 04 f0 22 00 0f 05 01 10 16 01 30 67 01 30 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-08-11 16:38:41.231245: ✅ 数据解析成功，开始提取标签信息
2025-08-11 16:38:41.231245: 🔍 LSGControlCenter数据结构分析:
2025-08-11 16:38:41.231245:    数据总长度: 58 bytes
2025-08-11 16:38:41.231245:    长度字节 rawBuffer[18] = 0x27 = 39
2025-08-11 16:38:41.231245:    15962数据起始位置: 索引19 (应为0x41)
2025-08-11 16:38:41.232243:    15962数据长度: 39 bytes
2025-08-11 16:38:41.232243: 📋 LSGControlCenter事件信息: eventType=0, direction=0, dataLen=39
2025-08-11 16:38:41.232243: 📅 时间戳: 0-9-17-5-31-5
2025-08-11 16:38:41.232243: 🔍 开始提取LSGControlCenter标签数据: 从索引19开始，长度39
2025-08-11 16:38:41.232243: 🔍 LSGControlCenter标签数据提取完成: 39字节
2025-08-11 16:38:41.232243: 🔍 提取的15962数据: 41 06 07 1c 30 c3 0c 75 02 01 b8 03 04 f0 22 00 0f 05 01 10 16 01 30 67 01 30 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-08-11 16:38:41.232243: 🏷️ 标签UID: 4106071C30C30C750201B80304F022000F05011016013067013000000000000000000000000000 (长度: 39 bytes)
2025-08-11 16:38:41.233240: 🏷️ 标签原始数据: 41 06 07 1c 30 c3 0c 75 02 01 b8 03 04 f0 22 00 0f 05 01 10 16 01 30 67 01 30 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-08-11 16:38:41.233240: 🔍 _decodeTagData调试信息:
2025-08-11 16:38:41.233240:    输入data: 4106071C30C30C750201B80304F022000F05011016013067013000000000000000000000000000
2025-08-11 16:38:41.233240:    输入uid: 4106071C30C30C750201B80304F022000F05011016013067013000000000000000000000000000
2025-08-11 16:38:41.233240:    数据长度: 78
2025-08-11 16:38:41.233240: 🔍 LSGate使用解码器: 15962标准协议
2025-08-11 16:38:41.233240: 🔍 设备类型: LSGControlCenter
2025-08-11 16:38:41.233240: 🔍 开始调用解码器...
2025-08-11 16:38:41.234236: 🔍 解码器类型: Coder15962Std
2025-08-11 16:38:41.234236: 🎯 解码成功: {"oidList":[{"oid":1,"compressMode":"100","data":"********","originHexStr":"4106071C30C30C75","originData":["07","1C","30","C3","0C","75"],"isKeepInTag":true},{"oid":2,"compressMode":"000","data":"B8","originHexStr":"0201B8","originData":["B8"],"isKeepInTag":true},{"oid":3,"compressMode":"000","data":"044001","originHexStr":"0304F022000F","originData":["F0","22","00","0F"],"isKeepInTag":true},{"oid":5,"compressMode":"000","data":"10","originHexStr":"050110","originData":["10"],"isKeepInTag":true},{"oid":6,"compressMode":"001","data":"48","originHexStr":"160130","originData":["30"],"isKeepInTag":true},{"oid":7,"compressMode":"110","data":"0","originHexStr":"670130","originData":["30"],"isKeepInTag":true}]}
2025-08-11 16:38:41.234236: 🔍 解码结果分析:
2025-08-11 16:38:41.234236:    barCode: null
2025-08-11 16:38:41.234236:    oidList: [{oid: 1, compressMode: 100, data: ********, originHexStr: 4106071C30C30C75, originData: [07, 1C, 30, C3, 0C, 75], isKeepInTag: true}, {oid: 2, compressMode: 000, data: B8, originHexStr: 0201B8, originData: [B8], isKeepInTag: true}, {oid: 3, compressMode: 000, data: 044001, originHexStr: 0304F022000F, originData: [F0, 22, 00, 0F], isKeepInTag: true}, {oid: 5, compressMode: 000, data: 10, originHexStr: 050110, originData: [10], isKeepInTag: true}, {oid: 6, compressMode: 001, data: 48, originHexStr: 160130, originData: [30], isKeepInTag: true}, {oid: 7, compressMode: 110, data: 0, originHexStr: 670130, originData: [30], isKeepInTag: true}]
2025-08-11 16:38:41.234236:    OID[0]: {oid: 1, compressMode: 100, data: ********, originHexStr: 4106071C30C30C75, originData: [07, 1C, 30, C3, 0C, 75], isKeepInTag: true}
2025-08-11 16:38:41.235234:    OID[1]: {oid: 2, compressMode: 000, data: B8, originHexStr: 0201B8, originData: [B8], isKeepInTag: true}
2025-08-11 16:38:41.235234:    OID[2]: {oid: 3, compressMode: 000, data: 044001, originHexStr: 0304F022000F, originData: [F0, 22, 00, 0F], isKeepInTag: true}
2025-08-11 16:38:41.235234:    OID[3]: {oid: 5, compressMode: 000, data: 10, originHexStr: 050110, originData: [10], isKeepInTag: true}
2025-08-11 16:38:41.235234:    OID[4]: {oid: 6, compressMode: 001, data: 48, originHexStr: 160130, originData: [30], isKeepInTag: true}
2025-08-11 16:38:41.235234:    OID[5]: {oid: 7, compressMode: 110, data: 0, originHexStr: 670130, originData: [30], isKeepInTag: true}
2025-08-11 16:38:41.235234: 🔍 解码结果: {"oidList":[{"oid":1,"compressMode":"100","data":"********","originHexStr":"4106071C30C30C75","originData":["07","1C","30","C3","0C","75"],"isKeepInTag":true},{"oid":2,"compressMode":"000","data":"B8","originHexStr":"0201B8","originData":["B8"],"isKeepInTag":true},{"oid":3,"compressMode":"000","data":"044001","originHexStr":"0304F022000F","originData":["F0","22","00","0F"],"isKeepInTag":true},{"oid":5,"compressMode":"000","data":"10","originHexStr":"050110","originData":["10"],"isKeepInTag":true},{"oid":6,"compressMode":"001","data":"48","originHexStr":"160130","originData":["30"],"isKeepInTag":true},{"oid":7,"compressMode":"110","data":"0","originHexStr":"670130","originData":["30"],"isKeepInTag":true}]}
2025-08-11 16:38:41.235234: 🎯 添加标签到结果列表: {uid: 4106071C30C30C750201B80304F022000F05011016013067013000000000000000000000000000, data: 4106071C30C30C750201B80304F022000F05011016013067013000000000000000000000000000, eventType: 0, direction: 0, time: [0, 9, 17, 5, 31, 5], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":1,"compressMode":"100","data":"********","originHexStr":"4106071C30C30C75","originData":["07","1C","30","C3","0C","75"],"isKeepInTag":true},{"oid":2,"compressMode":"000","data":"B8","originHexStr":"0201B8","originData":["B8"],"isKeepInTag":true},{"oid":3,"compressMode":"000","data":"044001","originHexStr":"0304F022000F","originData":["F0","22","00","0F"],"isKeepInTag":true},{"oid":5,"compressMode":"000","data":"10","originHexStr":"050110","originData":["10"],"isKeepInTag":true},{"oid":6,"compressMode":"001","data":"48","originHexStr":"160130","originData":["30"],"isKeepInTag":true},{"oid":7,"compressMode":"110","data":"0","originHexStr":"670130","originData":["30"],"isKeepInTag":true}]}}
2025-08-11 16:38:41.236232: 📋 获取下一个报告: hReport=0
2025-08-11 16:38:41.236232: 📊 LSGate扫描完成: 共返回 1 个标签
2025-08-11 16:38:41.236232: 📋 标签1: {uid: 4106071C30C30C750201B80304F022000F05011016013067013000000000000000000000000000, data: 4106071C30C30C750201B80304F022000F05011016013067013000000000000000000000000000, eventType: 0, direction: 0, time: [0, 9, 17, 5, 31, 5], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":1,"compressMode":"100","data":"********","originHexStr":"4106071C30C30C75","originData":["07","1C","30","C3","0C","75"],"isKeepInTag":true},{"oid":2,"compressMode":"000","data":"B8","originHexStr":"0201B8","originData":["B8"],"isKeepInTag":true},{"oid":3,"compressMode":"000","data":"044001","originHexStr":"0304F022000F","originData":["F0","22","00","0F"],"isKeepInTag":true},{"oid":5,"compressMode":"000","data":"10","originHexStr":"050110","originData":["10"],"isKeepInTag":true},{"oid":6,"compressMode":"001","data":"48","originHexStr":"160130","originData":["30"],"isKeepInTag":true},{"oid":7,"compressMode":"110","data":"0","originHexStr":"670130","originData":["30"],"isKeepInTag":true}]}}
2025-08-11 16:38:41.236232: 🔍 LSGate ReaderManager收到扫描结果: 1 个UID
2025-08-11 16:38:41.236232: 🏷️ LSGate UID[0]: {uid: 4106071C30C30C750201B80304F022000F05011016013067013000000000000000000000000000, data: 4106071C30C30C750201B80304F022000F05011016013067013000000000000000000000000000, eventType: 0, direction: 0, time: [0, 9, 17, 5, 31, 5], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":1,"compressMode":"100","data":"********","originHexStr":"4106071C30C30C75","originData":["07","1C","30","C3","0C","75"],"isKeepInTag":true},{"oid":2,"compressMode":"000","data":"B8","originHexStr":"0201B8","originData":["B8"],"isKeepInTag":true},{"oid":3,"compressMode":"000","data":"044001","originHexStr":"0304F022000F","originData":["F0","22","00","0F"],"isKeepInTag":true},{"oid":5,"compressMode":"000","data":"10","originHexStr":"050110","originData":["10"],"isKeepInTag":true},{"oid":6,"compressMode":"001","data":"48","originHexStr":"160130","originData":["30"],"isKeepInTag":true},{"oid":7,"compressMode":"110","data":"0","originHexStr":"670130","originData":["30"],"isKeepInTag":true}]}}
2025-08-11 16:38:41.236232: data:4106071C30C30C750201B80304F022000F05011016013067013000000000000000000000000000,coder:Coder15962Std
2025-08-11 16:38:41.237228: parseRet：{"oidList":[{"oid":1,"compressMode":"100","data":"********","originHexStr":"4106071C30C30C75","originData":["07","1C","30","C3","0C","75"],"isKeepInTag":true},{"oid":2,"compressMode":"000","data":"B8","originHexStr":"0201B8","originData":["B8"],"isKeepInTag":true},{"oid":3,"compressMode":"000","data":"044001","originHexStr":"0304F022000F","originData":["F0","22","00","0F"],"isKeepInTag":true},{"oid":5,"compressMode":"000","data":"10","originHexStr":"050110","originData":["10"],"isKeepInTag":true},{"oid":6,"compressMode":"001","data":"48","originHexStr":"160130","originData":["30"],"isKeepInTag":true},{"oid":7,"compressMode":"110","data":"0","originHexStr":"670130","originData":["30"],"isKeepInTag":true}]},decoder:15962标准协议
2025-08-11 16:38:41.237228: 🎯 HWTagProvider.changeAddedItem: 收到 1 个新标签
2025-08-11 16:38:41.237228: 🏷️ 新标签[0]: uid=4106071C30C30C750201B80304F022000F05011016013067013000000000000000000000000000, barCode=********, readerType=22
2025-08-11 16:38:41.237228: 📡 LSGate标签详情: {uid: 4106071C30C30C750201B80304F022000F05011016013067013000000000000000000000000000, barCode: ********, eas: null, tagType: null, libraryCode: 044001, afiStr: null, afiData: null, readerType: 22, decoderType: 15962标准协议, dsfID: null, oidList: [{oid: 1, compressMode: 100, data: ********, originHexStr: 4106071C30C30C75, originData: [07, 1C, 30, C3, 0C, 75], isKeepInTag: true}, {oid: 2, compressMode: 000, data: B8, originHexStr: 0201B8, originData: [B8], isKeepInTag: true}, {oid: 3, compressMode: 000, data: 044001, originHexStr: 0304F022000F, originData: [F0, 22, 00, 0F], isKeepInTag: true}, {oid: 5, compressMode: 000, data: 10, originHexStr: 050110, originData: [10], isKeepInTag: true}, {oid: 6, compressMode: 001, data: 48, originHexStr: 160130, originData: [30], isKeepInTag: true}, {oid: 7, compressMode: 110, data: 0, originHexStr: 670130, originData: [30], isKeepInTag: true}], data: 4106071C30C30C750201B80304F022000F05011016013067013000000000000000000000000000, leftBinary: null, sortType: null, codeType: null, version: null, contentIndex: null, tagFrequency: 0, info: null, inAnt: 1}
2025-08-11 16:38:41.237228: 🎯 HWTagProvider: 当前总标签数 1, 新增标签数 1
2025-08-11 16:38:41.238226: 检测到标签数据: 1条
2025-08-11 16:38:41.238226: 从barCode字段提取条码: ********
2025-08-11 16:38:41.238226: 从oidList提取条码: ********
2025-08-11 16:38:41.238585: 扫描到条码: ******** (总计: 1)
2025-08-11 16:38:41.238585: 强制实时获取书籍信息（不使用任何缓存）: ********
2025-08-11 16:38:41.238585: 请求书籍信息: ******** (第1次尝试)
2025-08-11 16:38:41.239090: 书籍扫描结果: ******** - BookScanStatus.processing - 未知
2025-08-11 16:38:41.239090: Req msgType：Sip2MsgType.itemInformation ,length:48， ret:  **********    163841AOhlsp|AB********|AY3AZF58B
2025-08-11 16:38:41.239090: 扫描到新条码: ******** (总计: 1)
2025-08-11 16:38:41.239090: 页面状态变更: SilencePageState.rfidScanning
2025-08-11 16:38:41.239090: 正在获取书籍信息: ********
2025-08-11 16:38:41.239090: 页面状态变更: SilencePageState.rfidScanning
2025-08-11 16:38:41.436687: Rsp : 180120250811    164025CK00BT01|AOhlsp|AA|AB********|AJ|AW|AK|AQ|AP|PB|CH|KC|AH|BT01|AF未找到此条码号的单件|AG|AY3AZCF8F
2025-08-11 16:38:41.456632: bookData:{CirculationStatus: 01, SecurityMaker: 20, FeeType: 25, InstitutionId: hlsp, PatronIdentifier: , ItemIdentifier: ********, DayCheckOutIn: null, TitleIdentifier: , ItemAuthor: , ISBN: , Reservation: null, MediaType: null, ItemProperties: , CallNo: , DueDate: , ItemCirtype: null, OrgLocation: , CurrentLocation: , ReservationRfid: null, Publisher: , Subject: null, PageNum: null, ShelfNo: null, ScreenMessage: 未找到此条码号的单件, PrintLine: }
2025-08-11 16:38:41.456632: 📚 条码 ******** 没有TitleIdentifier，可能是其他馆的条码，忽略
2025-08-11 16:38:41.456632: SIP2数据转换失败，可能是其他馆的条码: ********
2025-08-11 16:38:41.456632: SIP2未找到书籍信息: ******** (可能是其他馆的条码，忽略)
2025-08-11 16:38:41.456632: 书籍扫描结果: ******** - BookScanStatus.notFound - 未知
2025-08-11 16:38:41.457629: 未找到书籍信息: ******** - 可能是其他馆的条码，将被忽略
2025-08-11 16:38:41.457629: 页面状态变更: SilencePageState.rfidScanning
2025-08-11 16:38:41.593268: 🧹 LSGate开始新的扫描流程，清空历史缓存...
2025-08-11 16:38:41.595291: 🔍 RDR_BuffMode_ClearRecords调用结果: ret=0
2025-08-11 16:38:41.697018: 🔧 LSGate使用缓冲模式获取RFID事件记录...
2025-08-11 16:38:41.697988: 🔍 RDR_BuffMode_FetchRecords调用结果: ret=0, hReader=3047799166256, flag=0
2025-08-11 16:38:41.698985: 📊 LSGate扫描结果: 发现 0 个标签报告
2025-08-11 16:38:41.698985: 📭 没有标签报告可处理
2025-08-11 16:38:41.698985: 📊 LSGate扫描完成: 共返回 0 个标签
2025-08-11 16:38:41.698985: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-11 16:38:42.093763: 🧹 LSGate开始新的扫描流程，清空历史缓存...
2025-08-11 16:38:42.094735: 🔍 RDR_BuffMode_ClearRecords调用结果: ret=0
2025-08-11 16:38:42.196495: 🔧 LSGate使用缓冲模式获取RFID事件记录...
2025-08-11 16:38:42.197481: 🔍 RDR_BuffMode_FetchRecords调用结果: ret=0, hReader=3047799166256, flag=0
2025-08-11 16:38:42.198459: 📊 LSGate扫描结果: 发现 0 个标签报告
2025-08-11 16:38:42.198459: 📭 没有标签报告可处理
2025-08-11 16:38:42.198459: 📊 LSGate扫描完成: 共返回 0 个标签
2025-08-11 16:38:42.198459: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-11 16:38:42.593341: 🧹 LSGate开始新的扫描流程，清空历史缓存...
2025-08-11 16:38:42.595336: 🔍 RDR_BuffMode_ClearRecords调用结果: ret=0
2025-08-11 16:38:42.697081: 🔧 LSGate使用缓冲模式获取RFID事件记录...
2025-08-11 16:38:42.699059: 🔍 RDR_BuffMode_FetchRecords调用结果: ret=0, hReader=3047799166256, flag=0
2025-08-11 16:38:42.699059: 📊 LSGate扫描结果: 发现 0 个标签报告
2025-08-11 16:38:42.699059: 📭 没有标签报告可处理
2025-08-11 16:38:42.699059: 📊 LSGate扫描完成: 共返回 0 个标签
2025-08-11 16:38:42.699059: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-11 16:38:43.093650: 🧹 LSGate开始新的扫描流程，清空历史缓存...
2025-08-11 16:38:43.094648: 🔍 RDR_BuffMode_ClearRecords调用结果: ret=0
2025-08-11 16:38:43.196376: 🔧 LSGate使用缓冲模式获取RFID事件记录...
2025-08-11 16:38:43.198371: 🔍 RDR_BuffMode_FetchRecords调用结果: ret=0, hReader=3047799166256, flag=0
2025-08-11 16:38:43.198371: 📊 LSGate扫描结果: 发现 0 个标签报告
2025-08-11 16:38:43.198371: 📭 没有标签报告可处理
2025-08-11 16:38:43.198371: 📊 LSGate扫描完成: 共返回 0 个标签
2025-08-11 16:38:43.199368: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-11 16:38:43.593932: 🧹 LSGate开始新的扫描流程，清空历史缓存...
2025-08-11 16:38:43.595903: 🔍 RDR_BuffMode_ClearRecords调用结果: ret=0
2025-08-11 16:38:43.697387: 🔧 LSGate使用缓冲模式获取RFID事件记录...
2025-08-11 16:38:43.699381: 🔍 RDR_BuffMode_FetchRecords调用结果: ret=0, hReader=3047799166256, flag=0
2025-08-11 16:38:43.699381: 📊 LSGate扫描结果: 发现 0 个标签报告
2025-08-11 16:38:43.699381: 📭 没有标签报告可处理
2025-08-11 16:38:43.699381: 📊 LSGate扫描完成: 共返回 0 个标签
2025-08-11 16:38:43.700367: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-11 16:38:44.093440: 🧹 LSGate开始新的扫描流程，清空历史缓存...
2025-08-11 16:38:44.095428: 🔍 RDR_BuffMode_ClearRecords调用结果: ret=0
2025-08-11 16:38:44.197156: 🔧 LSGate使用缓冲模式获取RFID事件记录...
2025-08-11 16:38:44.199153: 🔍 RDR_BuffMode_FetchRecords调用结果: ret=0, hReader=3047799166256, flag=0
2025-08-11 16:38:44.199153: 📊 LSGate扫描结果: 发现 0 个标签报告
2025-08-11 16:38:44.199153: 📭 没有标签报告可处理
2025-08-11 16:38:44.199153: 📊 LSGate扫描完成: 共返回 0 个标签
2025-08-11 16:38:44.199153: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-11 16:38:44.593642: 🧹 LSGate开始新的扫描流程，清空历史缓存...
2025-08-11 16:38:44.595663: 🔍 RDR_BuffMode_ClearRecords调用结果: ret=0
2025-08-11 16:38:44.697388: 🔧 LSGate使用缓冲模式获取RFID事件记录...
2025-08-11 16:38:44.699358: 🔍 RDR_BuffMode_FetchRecords调用结果: ret=0, hReader=3047799166256, flag=0
2025-08-11 16:38:44.699358: 📊 LSGate扫描结果: 发现 0 个标签报告
2025-08-11 16:38:44.699358: 📭 没有标签报告可处理
2025-08-11 16:38:44.699358: 📊 LSGate扫描完成: 共返回 0 个标签
2025-08-11 16:38:44.699358: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-11 16:38:45.094415: 🧹 LSGate开始新的扫描流程，清空历史缓存...
2025-08-11 16:38:45.095413: 🔍 RDR_BuffMode_ClearRecords调用结果: ret=0
2025-08-11 16:38:45.197141: 🔧 LSGate使用缓冲模式获取RFID事件记录...
2025-08-11 16:38:45.198138: 🔍 RDR_BuffMode_FetchRecords调用结果: ret=0, hReader=3047799166256, flag=0
2025-08-11 16:38:45.199136: 📊 LSGate扫描结果: 发现 0 个标签报告
2025-08-11 16:38:45.199136: 📭 没有标签报告可处理
2025-08-11 16:38:45.199136: 📊 LSGate扫描完成: 共返回 0 个标签
2025-08-11 16:38:45.199136: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-11 16:38:45.593676: 🧹 LSGate开始新的扫描流程，清空历史缓存...
2025-08-11 16:38:45.595647: 🔍 RDR_BuffMode_ClearRecords调用结果: ret=0
2025-08-11 16:38:45.696733: 🔧 LSGate使用缓冲模式获取RFID事件记录...
2025-08-11 16:38:45.697731: 🔍 RDR_BuffMode_FetchRecords调用结果: ret=0, hReader=3047799166256, flag=0
2025-08-11 16:38:45.698728: 📊 LSGate扫描结果: 发现 0 个标签报告
2025-08-11 16:38:45.698728: 📭 没有标签报告可处理
2025-08-11 16:38:45.698728: 📊 LSGate扫描完成: 共返回 0 个标签
2025-08-11 16:38:45.698728: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-11 16:38:45.718674: 收到串口命令: position_reached (到达指定位置)
2025-08-11 16:38:45.718674: 用户到达指定位置，准备停止RFID扫描
2025-08-11 16:38:45.719672: 闸机状态变更: GateState.exitChecking
2025-08-11 16:38:45.719672: 停止增强RFID扫描
2025-08-11 16:38:45.719672: 停止增强RFID扫描
2025-08-11 16:38:45.719672: stopInventory newPort:SendPort
2025-08-11 16:38:45.719672: 增强RFID扫描已停止，共扫描到1个条码
2025-08-11 16:38:45.719672: 增强RFID扫描已停止，共扫描到1个条码
2025-08-11 16:38:45.719672: RFID扫描结束，共扫描到1本书
2025-08-11 16:38:45.720668: 开始检查书籍状态，共1本书
2025-08-11 16:38:45.720668: 实时检查条码: ********
2025-08-11 16:38:45.720668: 强制实时获取书籍信息（不使用任何缓存）: ********
2025-08-11 16:38:45.720668: 请求书籍信息: ******** (第1次尝试)
2025-08-11 16:38:45.720668: Req msgType：Sip2MsgType.itemInformation ,length:48， ret:  **********    163845AOhlsp|AB********|AY4AZF586
2025-08-11 16:38:45.720668: subThread :ReaderCommand.stopInventory
2025-08-11 16:38:45.721665: commandRsp:ReaderCommand.stopInventory
2025-08-11 16:38:45.815415: Rsp : 180120250811    164029CK00BT01|AOhlsp|AA|AB********|AJ|AW|AK|AQ|AP|PB|CH|KC|AH|BT01|AF未找到此条码号的单件|AG|AY4AZCF8A
2025-08-11 16:38:45.834380: bookData:{CirculationStatus: 01, SecurityMaker: 20, FeeType: 25, InstitutionId: hlsp, PatronIdentifier: , ItemIdentifier: ********, DayCheckOutIn: null, TitleIdentifier: , ItemAuthor: , ISBN: , Reservation: null, MediaType: null, ItemProperties: , CallNo: , DueDate: , ItemCirtype: null, OrgLocation: , CurrentLocation: , ReservationRfid: null, Publisher: , Subject: null, PageNum: null, ShelfNo: null, ScreenMessage: 未找到此条码号的单件, PrintLine: }
2025-08-11 16:38:45.834380: 📚 条码 ******** 没有TitleIdentifier，可能是其他馆的条码，忽略
2025-08-11 16:38:45.834380: SIP2数据转换失败，可能是其他馆的条码: ********
2025-08-11 16:38:45.834380: 条码 ******** 未找到书籍信息，忽略（可能是其他馆的条码）
2025-08-11 16:38:45.835362: 检查结果: 有效书籍0本，忽略条码1个
2025-08-11 16:38:45.835362: 所有条码都是其他馆的，允许通过
2025-08-11 16:38:45.835362: 页面状态变更: SilencePageState.exitAllowed
2025-08-11 16:38:45.835362: 发送闸机命令: GateCommand.exitOpen, 数据: aa 00 01 01 00 00 48 36
2025-08-11 16:38:45.835362: 发送数据失败: 期望发送8字节，实际发送7字节
2025-08-11 16:38:45.835362: 串口错误: SEND_INCOMPLETE - 发送数据不完整: 期望8字节，实际7字节
2025-08-11 16:38:45.835362: 闸机命令发送失败: exit_open (硬件可能未连接，但不影响系统功能)
2025-08-11 16:38:48.835975: 闸机认证服务未在运行
2025-08-11 16:38:48.835975: 闸机状态变更: GateState.idle
2025-08-11 16:38:48.835975: 页面状态变更: SilencePageState.welcome
