import 'package:flutter/material.dart';
import 'package:a3g/core/utils/window_util.dart';
import '../../../shared/utils/asset_util.dart';

/// 欢迎界面内容组件
class WelcomeContent extends StatefulWidget {
  const WelcomeContent({Key? key}) : super(key: key);

  @override
  State<WelcomeContent> createState() => _WelcomeContentState();
}

class _WelcomeContentState extends State<WelcomeContent>
    with TickerProviderStateMixin {
  
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
  }
  
  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: Duration(seconds: 2),
      vsync: this,
    );
    
    _scaleController = AnimationController(
      duration: Duration(seconds: 3),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));
  }
  
  void _startAnimations() {
    _fadeController.forward();
    _scaleController.forward();
  }
  
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_fadeAnimation, _scaleAnimation]),
      builder: (context, child) {
        return Opacity(
          opacity: _fadeAnimation.value,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 清华大学Logo
                Container(
                  margin: EdgeInsets.only(bottom: 20.p),
                  child: Image.asset(
                    AssetUtil.fullPath('tsinghua_big_logo'),
                    width: 493.p,
                    height: 304.p,
                  ),
                ),
                
                SizedBox(height: 8.p),
                
                // 欢迎文字
                Text(
                  '欢迎您',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 64.p,
                    fontWeight: FontWeight.w300,
                    letterSpacing: 2.0,
                  ),
                ),
                
                SizedBox(height: 40.p),
                
                // 提示文字
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 40.p, vertical: 20.p),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(25.p),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    '请靠近闸机进行身份认证',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.9),
                      fontSize: 24.p,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
  
  @override
  void dispose() {
    _fadeController.dispose();
    _scaleController.dispose();
    super.dispose();
  }
}
