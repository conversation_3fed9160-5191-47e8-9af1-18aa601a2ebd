import 'package:flutter/material.dart';
import 'package:a3g/core/utils/window_util.dart';
import '../widgets/tip_card.dart';
import '../widgets/radar_scan_tip_card.dart';
import '../../../shared/utils/asset_util.dart';

/// 演示页面，展示集成后的组件效果
class DemoPage extends StatefulWidget {
  const DemoPage({super.key});

  @override
  State<DemoPage> createState() => _DemoPageState();
}

class _DemoPageState extends State<DemoPage> {
  int _currentIndex = 0;
  
  final List<String> _demoTitles = [
    '认证成功演示',
    '认证失败演示', 
    '检测到未借书籍演示',
    '扫描中演示',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_demoTitles[_currentIndex]),
        backgroundColor: Colors.blue.shade800,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              setState(() {
                // 刷新当前演示
              });
            },
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(AssetUtil.fullPath('tsinghua_bg')),
            fit: BoxFit.cover,
          ),
        ),
        child: Center(
          child: _buildCurrentDemo(),
        ),
      ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.check_circle),
            label: '认证成功',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.error),
            label: '认证失败',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.book),
            label: '未借书籍',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.radar),
            label: '扫描中',
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentDemo() {
    switch (_currentIndex) {
      case 0:
        return _buildAuthSuccessDemo();
      case 1:
        return _buildAuthFailedDemo();
      case 2:
        return _buildUnborrowedBooksDemo();
      case 3:
        return _buildScanningDemo();
      default:
        return _buildAuthSuccessDemo();
    }
  }

  /// 认证成功演示
  Widget _buildAuthSuccessDemo() {
    return Center(
      child: TipCard(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 60.p,
            ),
            SizedBox(height: 20.p),
            Text(
              '认证成功',
              style: TextStyle(
                color: Colors.green,
                fontSize: 32.p,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 15.p),
            Text(
              '张某某，同学',
              style: TextStyle(
                color: const Color(0xFF242424),
                fontSize: 28.p,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 10.p),
            Text(
              '欢迎再次光临',
              style: TextStyle(
                color: const Color(0xFF242424),
                fontSize: 20.p,
                fontWeight: FontWeight.w400,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// 认证失败演示
  Widget _buildAuthFailedDemo() {
    return Center(
      child: TipCard(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error,
              color: Colors.red,
              size: 60.p,
            ),
            SizedBox(height: 20.p),
            Text(
              '认证失败',
              style: TextStyle(
                color: Colors.red,
                fontSize: 32.p,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 15.p),
            Text(
              '未检测到借者信息，请先办理读者证',
              style: TextStyle(
                color: const Color(0xFF242424),
                fontSize: 20.p,
                fontWeight: FontWeight.w400,
                height: 1.3,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  /// 检测到未借书籍演示
  Widget _buildUnborrowedBooksDemo() {
    return const Center(
      child: RadarScanTipCard(
        number: 3,
        state: RadarScanState.detected,
      ),
    );
  }

  /// 扫描中演示
  Widget _buildScanningDemo() {
    return const Center(
      child: RadarScanTipCard(
        number: 2,
        state: RadarScanState.scanning,
      ),
    );
  }
}
