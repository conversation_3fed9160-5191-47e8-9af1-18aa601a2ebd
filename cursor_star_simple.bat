@echo off
title cursor_star Monitor Simple

REM ==================== Configuration ====================
set "PROCESS_NAME=cursor_star.exe"
set "EXE_PATH=C:\Users\<USER>\Desktop\Release\cursor_star.exe"
set "WORK_DIR=C:\Users\<USER>\Desktop\Release"
set "CHECK_INTERVAL=10"
REM ==================== Configuration End ====================

echo cursor_star Monitor Started
echo Process: %PROCESS_NAME%
echo Path: %EXE_PATH%
echo Work Dir: %WORK_DIR%
echo Interval: %CHECK_INTERVAL%s
echo.

if not exist "%EXE_PATH%" (
    echo ERROR: File not found: %EXE_PATH%
    pause
    exit
)

if not exist "%WORK_DIR%" (
    echo ERROR: Directory not found: %WORK_DIR%
    pause
    exit
)

echo Starting monitor...

:LOOP
    tasklist /FI "IMAGENAME eq %PROCESS_NAME%" 2>NUL | find /I "%PROCESS_NAME%" >NUL
    
    if %ERRORLEVEL% EQU 0 (
        echo %time% - Running OK
    ) else (
        echo %time% - Not running, starting...
        cd /d "%WORK_DIR%"
        start "" "%EXE_PATH%"
        echo %time% - Started
    )
    
    timeout /t %CHECK_INTERVAL% /nobreak >nul
    goto LOOP
