软件监控脚本 - 简单使用说明

==================== 使用步骤 ====================

1. 修改配置
   打开 "软件监控脚本.ps1" 文件，修改以下三行：
   
   $ProcessName = "a3g"                    # 改为你的软件名（不含.exe）
   $ExecutablePath = "C:\Users\<USER>\Desktop\a3g\build\release\a3g.exe"    # 改为你的软件完整路径
   $CheckInterval = 10                       # 检查间隔秒数（可选）

2. 测试运行
   双击 "启动监控脚本.bat" 测试是否正常工作

3. 设置开机自启
   方法一：复制 "启动监控脚本.bat" 到启动文件夹
   - 按 Win+R，输入 shell:startup，回车
   - 把 "启动监控脚本.bat" 复制到打开的文件夹
   
   方法二：使用任务计划程序（推荐）
   - 按 Win+R，输入 taskschd.msc，回车
   - 右侧点击"创建基本任务"
   - 名称：软件监控
   - 触发器：计算机启动时
   - 操作：启动程序
   - 程序：选择 "启动监控脚本.bat" 的完整路径

==================== 注意事项 ====================

- 脚本已处理中文乱码问题
- 进程名就是exe文件名去掉.exe
- 建议先测试正常工作后再设置开机自启
- 要停止监控，关闭PowerShell窗口即可

==================== 示例配置 ====================

如果你的软件是 QQ.exe，放在 C:\Program Files\Tencent\QQ\ 文件夹：

$ProcessName = "QQ"
$ExecutablePath = "C:\Program Files\Tencent\QQ\QQ.exe"
$CheckInterval = 10
