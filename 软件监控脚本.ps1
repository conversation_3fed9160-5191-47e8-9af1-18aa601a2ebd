# 软件监控脚本
# 功能：每10秒检查软件是否运行，如果没有运行则启动

# ==================== 配置区域 ====================
# 请根据你的软件修改以下配置
$ProcessName = "MyApp"             # 你的软件进程名（不包含.exe）
$ExecutablePath = "D:\MyApp\MyApp.exe"       # 你的软件完整路径
$WorkingDirectory = "D:\MyApp"               # 软件工作目录（软件所在文件夹）
$StartupArguments = ""             # 启动参数（绿色软件通常不需要）
$CheckInterval = 10                # 检查间隔（秒）
$LogFile = "D:\MyApp\Logs\AppMonitor.log"   # 日志文件路径（建议放在软件目录下）
# ==================== 配置区域结束 ====================

# 创建日志目录
$LogDir = Split-Path $LogFile -Parent
if (!(Test-Path $LogDir)) {
    New-Item -ItemType Directory -Path $LogDir -Force
}

# 验证软件路径是否存在
if (!(Test-Path $ExecutablePath)) {
    Write-Host "错误: 找不到软件文件 $ExecutablePath" -ForegroundColor Red
    Write-Host "请检查软件路径是否正确！" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 写日志函数
function Write-Log {
    param([string]$Message)
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogMessage = "[$Timestamp] $Message"
    Write-Host $LogMessage
    Add-Content -Path $LogFile -Value $LogMessage
}

# 检查进程是否运行
function Test-ProcessRunning {
    param([string]$ProcessName)
    $Process = Get-Process -Name $ProcessName -ErrorAction SilentlyContinue
    return $Process -ne $null
}

# 启动软件
function Start-Application {
    try {
        Write-Log "正在启动软件: $ExecutablePath"
        
        if (!(Test-Path $ExecutablePath)) {
            Write-Log "错误: 找不到可执行文件 $ExecutablePath"
            return $false
        }
        
        $StartInfo = New-Object System.Diagnostics.ProcessStartInfo
        $StartInfo.FileName = $ExecutablePath
        $StartInfo.WorkingDirectory = $WorkingDirectory
        $StartInfo.Arguments = $StartupArguments
        $StartInfo.UseShellExecute = $true
        
        $Process = [System.Diagnostics.Process]::Start($StartInfo)
        
        if ($Process) {
            Write-Log "软件启动成功，进程ID: $($Process.Id)"
            return $true
        } else {
            Write-Log "软件启动失败"
            return $false
        }
    }
    catch {
        Write-Log "启动软件时发生错误: $($_.Exception.Message)"
        return $false
    }
}

# 主监控循环
function Start-Monitoring {
    Write-Log "开始监控软件: $ProcessName"
    Write-Log "检查间隔: $CheckInterval 秒"
    Write-Log "可执行文件: $ExecutablePath"
    
    while ($true) {
        try {
            if (Test-ProcessRunning -ProcessName $ProcessName) {
                Write-Log "软件正在运行"
            } else {
                Write-Log "软件未运行，正在启动..."
                Start-Application
            }
            
            Start-Sleep -Seconds $CheckInterval
        }
        catch {
            Write-Log "监控过程中发生错误: $($_.Exception.Message)"
            Start-Sleep -Seconds $CheckInterval
        }
    }
}

# 脚本入口点
Write-Log "==================== 软件监控脚本启动 ===================="
Write-Log "脚本版本: 1.0"
Write-Log "监控进程: $ProcessName"

# 首次启动检查
if (!(Test-ProcessRunning -ProcessName $ProcessName)) {
    Write-Log "首次检查发现软件未运行，正在启动..."
    Start-Application
}

# 开始监控
Start-Monitoring
