# 软件监控脚本 - 简化版
# 处理中文编码
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

# ==================== 配置区域 ====================
$ProcessName = "cursor_star"                                                    # 软件进程名（不含.exe）
$ExecutablePath = "C:\Users\<USER>\Desktop\Release\cursor_star.exe"               # 软件完整路径
$CheckInterval = 10                                                      # 检查间隔（秒）
# ==================== 配置区域结束 ====================

Write-Host "软件监控脚本启动..." -ForegroundColor Green
Write-Host "监控进程: $ProcessName" -ForegroundColor Cyan
Write-Host "软件路径: $ExecutablePath" -ForegroundColor Cyan
Write-Host "检查间隔: $CheckInterval 秒" -ForegroundColor Cyan

# 验证软件是否存在
if (!(Test-Path $ExecutablePath)) {
    Write-Host "错误: 找不到软件 $ExecutablePath" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit
}

Write-Host "开始监控..." -ForegroundColor Green

while ($true) {
    # 检查进程是否运行
    $Process = Get-Process -Name $ProcessName -ErrorAction SilentlyContinue

    if ($Process) {
        Write-Host "$(Get-Date -Format 'HH:mm:ss') - 软件正在运行" -ForegroundColor Green
    } else {
        Write-Host "$(Get-Date -Format 'HH:mm:ss') - 软件未运行，正在启动..." -ForegroundColor Yellow

        try {
            Start-Process -FilePath $ExecutablePath -WorkingDirectory (Split-Path $ExecutablePath)
            Write-Host "$(Get-Date -Format 'HH:mm:ss') - 软件启动成功" -ForegroundColor Green
        }
        catch {
            Write-Host "$(Get-Date -Format 'HH:mm:ss') - 启动失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    }

    Start-Sleep -Seconds $CheckInterval
}
