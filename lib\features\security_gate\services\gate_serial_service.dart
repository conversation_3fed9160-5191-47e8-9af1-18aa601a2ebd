import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:hardware/hardware.dart' as hw;

import '../models/gate_command.dart';
import 'package:seasetting/seasetting.dart';
import 'package:provider/provider.dart';
import 'package:get/get.dart';

/// 闸机串口通信服务
class GateSerialService {
  static GateSerialService? _instance;
  static GateSerialService get instance => _instance ??= GateSerialService._();
  GateSerialService._();

  // 统一改用硬件库 GateSerialManager 作为串口通道
  final hw.GateSerialManager _gateManager = hw.GateSerialManager.instance;
  StreamSubscription? _gateEventSub;

  // 状态标志
  bool _isInitialized = false;
  bool _isListening = false;

  // 配置参数
  String _portName = 'COM1';
  int _baudRate = 9600;

  // 事件流
  final StreamController<GateCommand> _commandController =
      StreamController<GateCommand>.broadcast();
  Stream<GateCommand> get commandStream => _commandController.stream;

  // 错误流
  final StreamController<String> _errorController =
      StreamController<String>.broadcast();
  Stream<String> get errorStream => _errorController.stream;

  /// 初始化串口服务
  Future<void> initialize({
    String? portName,
    int? baudRate,
  }) async {
    if (_isInitialized) {
      debugPrint('闸机串口服务已经初始化');
      return;
    }

    // 优先使用传入的参数，否则从SettingProvider获取安全闸机配置
    if (portName != null || baudRate != null) {
      _portName = portName ?? _portName;
      _baudRate = baudRate ?? _baudRate;
    } else {
      // 从SettingProvider获取安全闸机配置
      await _loadConfigFromSettingProvider();
    }

    try {
      await _openSerialPort();
      _isInitialized = true;
      debugPrint('闸机串口服务初始化成功: $_portName');
    } catch (e) {
      final errorMsg = '闸机串口服务初始化失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      rethrow;
    }
  }

  /// 开始监听串口数据
  Future<void> startListening() async {
    if (!_isInitialized) {
      throw Exception('串口服务未初始化');
    }

    if (_isListening) {
      debugPrint('串口监听已经启动');
      return;
    }

    try {
      _startReading();
      _isListening = true;
      debugPrint('开始监听闸机串口命令');
    } catch (e) {
      final errorMsg = '启动串口监听失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      rethrow;
    }
  }

  /// 停止监听串口数据
  Future<void> stopListening() async {
    if (!_isListening) return;
    try {
      await _gateEventSub?.cancel();
      _gateEventSub = null;
      _isListening = false;
      debugPrint('停止监听闸机串口命令');
    } catch (e) {
      debugPrint('停止串口监听失败: $e');
    }
  }

  /// 发送命令到闸机
  Future<bool> sendCommand(String commandType) async {
    if (!_isInitialized || !_gateManager.isConnected) {
      debugPrint('串口未初始化或未连接，模拟发送命令: $commandType');
      // 在没有真实硬件时，模拟命令发送成功，不影响其他功能
      return true;
    }

    try {
      bool result = false;
      switch (commandType) {
        case 'enter_open':
          result = await _gateManager.sendEnterOpenCommand();
          break;
        case 'exit_open':
          result = await _gateManager.sendExitOpenCommand();
          break;
        case 'failure_signal':
          result = await _gateManager.sendFailSignal();
          break;
        default:
          // 兜底：若存在原始字节映射，则直接发送底层字节
          final bytes = GateCommand.getSendCommandData(commandType);
          if (bytes != null) {
            result = await _gateManager
                .sendGateCommand(_inferEnumFromBytes(bytes) ?? hw.GateCommand.failSignal);
          } else {
            debugPrint('未知的发送命令类型: $commandType');
            return false;
          }
          break;
      }

      if (result) {
        debugPrint('闸机命令发送成功: $commandType');
      } else {
        debugPrint('闸机命令发送失败: $commandType (硬件可能未连接，但不影响系统功能)');
      }

      return result;
    } catch (e) {
      final errorMsg = '发送闸机命令异常: $commandType, 错误: $e (硬件可能未连接，但不影响系统功能)';
      debugPrint(errorMsg);
      // 不再将硬件发送失败作为系统错误，避免影响其他功能
      // _errorController.add(errorMsg);
      return false;
    }
  }

  /// 打开串口
  Future<void> _openSerialPort() async {
    try {
      // 优先使用配置端口，否则尝试第一个可用端口
      final ports = await _gateManager.getAvailablePorts();
      String? target = ports.contains(_portName) ? _portName : (ports.isNotEmpty ? ports.first : null);
      if (target == null) {
        throw Exception('未发现可用串口');
      }
      final ok = await _gateManager.connectGate(target, baudRate: _baudRate);
      if (!ok) {
        throw Exception('连接串口失败: $target');
      }

      // 建立事件订阅（接收硬件命令）
      _gateEventSub?.cancel();
      _gateEventSub = _gateManager.gateEventStream.listen((event) {
        if (event is hw.GateCommandReceivedEvent) {
          _handleGateReceived(event);
        }
      });

      debugPrint('串口 $target 连接成功 (波特率: $_baudRate)');
    } catch (e) {
      throw Exception('打开串口失败: $e');
    }
  }

  /// 开始读取串口数据
  void _startReading() {
    // 已由 _openSerialPort 中建立 gateEventStream 订阅
    debugPrint('开始监听串口数据（通过 GateSerialManager 事件流）');
  }

  /// 处理接收到的数据
  void _handleGateReceived(hw.GateCommandReceivedEvent evt) {
    if (!_isListening) return;
    try {
      final localType = _mapHwToLocalType(evt.command);
      if (localType != null) {
        final cmd = GateCommand(type: localType, data: evt.rawData);
        debugPrint('解析到闸机命令: ${cmd.type} (${cmd.displayName})');
        _commandController.add(cmd);
      } else {
        debugPrint('收到未映射的闸机命令: ${evt.command}');
      }
    } catch (e) {
      final errorMsg = '解析闸机命令失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
    }
  }

  /// 获取串口状态信息
  Map<String, dynamic> getStatus() {
    final info = _gateManager.connectionInfo;
    return {
      'initialized': _isInitialized,
      'listening': _isListening,
      'port_name': info['portName'],
      'baud_rate': info['baudRate'],
      'port_open': info['isConnected'] == true,
      'available_ports': 'N/A',
    };
  }

  /// 重新连接串口
  Future<bool> reconnect() async {
    try {
      debugPrint('尝试重新连接串口...');
      await stopListening();
      await _gateManager.disconnectGate();
      _isInitialized = false;
      await initialize(portName: _portName, baudRate: _baudRate);
      await startListening();
      debugPrint('串口重新连接成功');
      return true;
    } catch (e) {
      final errorMsg = '串口重新连接失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      return false;
    }
  }

  /// 检查串口连接状态
  bool get isConnected {
    return _isInitialized && _gateManager.isConnected;
  }

  /// 释放资源
  void dispose() {
    debugPrint('释放闸机串口服务资源');
    stopListening();
    _gateManager.disconnectGate();
    _commandController.close();
    _errorController.close();
    _isInitialized = false;
    _isListening = false;
    debugPrint('闸机串口服务已释放');
  }
  // 硬件命令 → 本地字符串类型映射
  String? _mapHwToLocalType(hw.GateCommand cmd) {
    switch (cmd) {
      case hw.GateCommand.enterStart:
        return GateCommand.enterStart;
      case hw.GateCommand.enterEnd:
        return GateCommand.enterEnd;
      case hw.GateCommand.exitStart:
        return GateCommand.exitStart;
      case hw.GateCommand.exitEnd:
        return GateCommand.exitEnd;
      case hw.GateCommand.reachPosition:
        return GateCommand.positionReached;
      case hw.GateCommand.tailgating:
        return GateCommand.tailgating;
      case hw.GateCommand.doorHasPerson:
        return GateCommand.doorBlocked;
      case hw.GateCommand.enterOpen:
      case hw.GateCommand.exitOpen:
      case hw.GateCommand.failSignal:
        return null; // 发送类命令不作为接收处理
    }
  }

  // 根据发送字节推断硬件枚举（兜底）
  hw.GateCommand? _inferEnumFromBytes(List<int> bytes) {
    if (_listEquals(bytes, [0xAA, 0x00, 0x02, 0x01, 0x00, 0x00, 0x48, 0x72])) {
      return hw.GateCommand.enterOpen;
    }
    if (_listEquals(bytes, [0xAA, 0x00, 0x01, 0x01, 0x00, 0x00, 0x48, 0x36])) {
      return hw.GateCommand.exitOpen;
    }
    return null;
  }

  bool _listEquals(List<int> a, List<int> b) {
    if (a.length != b.length) return false;
    for (int i = 0; i < a.length; i++) {
      if (a[i] != b[i]) return false;
    }
    return true;
  }

  /// 从SettingProvider加载安全闸机配置
  Future<void> _loadConfigFromSettingProvider() async {
    try {
      // 获取SettingProvider中的安全闸机配置
      final settingProvider = Get.context?.read<SettingProvider>();

      if (settingProvider != null) {
        // 从SettingProvider获取安全闸机配置
        final configs = await settingProvider.getSecurityGateConfigs();

        // 查找启用的配置
        final enabledConfigs = configs.where((config) => config.enabled).toList();
        if (enabledConfigs.isNotEmpty) {
          final config = enabledConfigs.first;
          _portName = config.serialConfig.gatePort;
          _baudRate = config.serialConfig.baudRate;
          debugPrint('从SettingProvider获取安全闸机配置: $_portName @ $_baudRate');
        } else {
          debugPrint('没有找到启用的安全闸机配置，使用默认配置');
        }
      } else {
        // 备用方案：直接从数据库查询
        await _loadConfigFromDatabase();
      }

      debugPrint('安全闸机配置加载完成: $_portName @ $_baudRate');
    } catch (e) {
      debugPrint('加载安全闸机配置失败: $e，使用默认配置');
    }
  }

  /// 临时方案：直接从数据库加载安全闸机配置
  Future<void> _loadConfigFromDatabase() async {
    try {
      final db = await DBSettingManager.getDBInstance();
      final configList = await db.querySettingBy('SecurityGateConfigs');

      if (configList.isNotEmpty) {
        final configsJson = configList.first;
        final configsData = jsonDecode(configsJson) as Map<String, dynamic>;

        // 查找启用的配置
        for (final entry in configsData.entries) {
          if (entry.value is Map<String, dynamic>) {
            final config = entry.value as Map<String, dynamic>;
            if (config['enabled'] == true) {
              final serialConfig = config['serialConfig'] as Map<String, dynamic>?;
              if (serialConfig != null) {
                _portName = serialConfig['gatePort'] ?? _portName;
                _baudRate = serialConfig['baudRate'] ?? _baudRate;
                debugPrint('从数据库获取安全闸机配置: $_portName @ $_baudRate');
                return;
              }
            }
          }
        }
      }

      debugPrint('数据库中没有找到启用的安全闸机配置，使用默认配置');

      debugPrint('安全闸机配置加载完成: $_portName @ $_baudRate');
    } catch (e) {
      debugPrint('加载安全闸机配置失败: $e，使用默认配置');
    }
  }

}
