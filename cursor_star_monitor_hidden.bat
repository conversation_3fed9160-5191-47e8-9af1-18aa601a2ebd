@echo off
REM This script runs the monitor in hidden mode

REM Create VBS script to run batch file hidden
echo Set WshShell = CreateObject("WScript.Shell") > "%temp%\run_hidden.vbs"
echo WshShell.Run chr(34) ^& "%~dp0cursor_star_monitor_background.bat" ^& Chr(34), 0 >> "%temp%\run_hidden.vbs"
echo Set WshShell = Nothing >> "%temp%\run_hidden.vbs"

REM Run the VBS script
cscript //nologo "%temp%\run_hidden.vbs"

REM Clean up
del "%temp%\run_hidden.vbs"
