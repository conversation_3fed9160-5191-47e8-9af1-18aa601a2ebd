import 'package:flutter/material.dart';
import 'book_info.dart';

/// 静默页面状态枚举
enum SilencePageState {
  welcome,          // 欢迎界面（对应 idle）
  authenticating,   // 认证中（对应 enterScanning）
  authSuccess,      // 认证成功
  authFailed,       // 认证失败
  rfidScanning,     // RFID扫描中（对应 exitScanning）
  bookChecking,     // 书籍检查（对应 exitChecking）
  exitAllowed,      // 允许出馆
  exitBlocked,      // 禁止出馆
  error,            // 错误状态
}

/// 页面状态扩展
extension SilencePageStateExtension on SilencePageState {
  /// 获取状态显示名称
  String get displayName {
    switch (this) {
      case SilencePageState.welcome:
        return '欢迎使用';
      case SilencePageState.authenticating:
        return '身份认证中';
      case SilencePageState.authSuccess:
        return '认证成功';
      case SilencePageState.authFailed:
        return '认证失败';
      case SilencePageState.rfidScanning:
        return '扫描随身物品';
      case SilencePageState.bookChecking:
        return '检查书籍状态';
      case SilencePageState.exitAllowed:
        return '检查通过';
      case SilencePageState.exitBlocked:
        return '检测到未借书籍';
      case SilencePageState.error:
        return '系统异常';
    }
  }
  
  /// 是否显示默认内容
  bool get showDefaultContent {
    return this == SilencePageState.welcome;
  }
  
  /// 是否为临时状态（需要自动恢复）
  bool get isTemporaryState {
    return [
      SilencePageState.authSuccess,
      SilencePageState.authFailed,
      SilencePageState.exitAllowed,
      SilencePageState.exitBlocked,
    ].contains(this);
  }
  
  /// 获取自动恢复时间（秒）
  int get autoRecoverSeconds {
    switch (this) {
      case SilencePageState.authSuccess:
      case SilencePageState.exitAllowed:
        return 3;
      case SilencePageState.authFailed:
      case SilencePageState.exitBlocked:
        return 5;
      default:
        return 0;
    }
  }
  
  /// 获取状态颜色
  Color get stateColor {
    switch (this) {
      case SilencePageState.welcome:
        return Colors.blue;
      case SilencePageState.authenticating:
      case SilencePageState.rfidScanning:
      case SilencePageState.bookChecking:
        return Colors.orange;
      case SilencePageState.authSuccess:
      case SilencePageState.exitAllowed:
        return Colors.green;
      case SilencePageState.authFailed:
      case SilencePageState.exitBlocked:
      case SilencePageState.error:
        return Colors.red;
    }
  }
}

/// UI内容数据模型
class UIContentData {
  final String? title;
  final String? message;
  final String? userName;
  final List<BookInfo>? books;
  final bool? isSuccess;
  final int? scannedCount;
  final Map<String, dynamic>? extraData;
  
  const UIContentData({
    this.title,
    this.message,
    this.userName,
    this.books,
    this.isSuccess,
    this.scannedCount,
    this.extraData,
  });
  
  /// 创建认证成功内容
  factory UIContentData.authSuccess({
    required String userName,
    String? message,
  }) {
    return UIContentData(
      title: '认证成功',
      message: message ?? '$userName，欢迎光临',
      userName: userName,
      isSuccess: true,
    );
  }
  
  /// 创建认证失败内容
  factory UIContentData.authFailed({
    String? message,
  }) {
    return UIContentData(
      title: '认证失败',
      message: message ?? '未找到读者信息，请先办理读者证',
      isSuccess: false,
    );
  }
  
  /// 创建书籍检查内容
  factory UIContentData.bookCheck({
    required List<BookInfo> books,
    required bool hasUnborrowedBooks,
  }) {
    return UIContentData(
      title: hasUnborrowedBooks ? '检测到未借书籍' : '检查通过',
      message: hasUnborrowedBooks 
          ? '请退出通道处理未借书籍' 
          : '所有书籍均已借阅，请通过',
      books: books,
      isSuccess: !hasUnborrowedBooks,
      extraData: {'has_unborrowed_books': hasUnborrowedBooks},
    );
  }
  
  /// 🔥 增强：创建RFID扫描中内容（支持书籍信息）
  factory UIContentData.rfidScanning({
    int scannedCount = 0,
    Map<String, dynamic>? booksInfo,
  }) {
    String message;
    if (scannedCount > 0) {
      final loadedCount = booksInfo?.length ?? 0;
      if (loadedCount > 0) {
        message = '已扫描 $scannedCount 件物品，获取到 $loadedCount 本书籍信息';
      } else {
        message = '已扫描 $scannedCount 件物品，正在获取书籍信息...';
      }
    } else {
      message = '请稍候...';
    }

    return UIContentData(
      title: '正在扫描随身物品',
      message: message,
      scannedCount: scannedCount,
      extraData: {
        'scanned_count': scannedCount,
        'books_info': booksInfo,
      },
    );
  }
  
  /// 创建认证中内容
  factory UIContentData.authenticating() {
    return const UIContentData(
      title: '身份认证',
      message: '请使用读者证或二维码进行认证',
    );
  }
  
  /// 创建错误内容
  factory UIContentData.error({
    required String message,
  }) {
    return UIContentData(
      title: '系统异常',
      message: message,
      isSuccess: false,
    );
  }
  
  /// 复制并修改数据
  UIContentData copyWith({
    String? title,
    String? message,
    String? userName,
    List<BookInfo>? books,
    bool? isSuccess,
    int? scannedCount,
    Map<String, dynamic>? extraData,
  }) {
    return UIContentData(
      title: title ?? this.title,
      message: message ?? this.message,
      userName: userName ?? this.userName,
      books: books ?? this.books,
      isSuccess: isSuccess ?? this.isSuccess,
      scannedCount: scannedCount ?? this.scannedCount,
      extraData: extraData ?? this.extraData,
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'message': message,
      'user_name': userName,
      'books': books?.map((book) => book.toJson()).toList(),
      'is_success': isSuccess,
      'scanned_count': scannedCount,
      'extra_data': extraData,
    };
  }
  
  @override
  String toString() {
    return 'UIContentData{title: $title, message: $message, userName: $userName}';
  }
}
