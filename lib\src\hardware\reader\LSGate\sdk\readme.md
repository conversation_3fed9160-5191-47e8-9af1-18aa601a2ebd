# LSGate图书馆安全门RFID阅读器SDK文档

## 📋 概述

LSGate（Library Security Gate）是专为图书馆安全门系统设计的高频RFID阅读器SDK。本SDK提供了完整的API接口，支持RFID标签检测、通行统计、红外传感器监控、系统时间管理等功能。

## 📁 文件结构

```
sdk/
├── rfidlib_reader.dll          # 主要API库文件
├── rfidlib_LSGate.dll          # LSGate特定功能库
├── Drivers/                    # 设备驱动程序目录
│   ├── rfidlib_drv_LSGControlCenter.dll  # LSG控制中心驱动
│   ├── rfidlib_LSG405.dll                # LSG405型号设备驱动
│   └── rfidlib_LSG606.dll                # LSG606型号设备驱动
└── readme.md                   # 本文档
```

## 🔧 核心API方法详解

### 1. 设备连接管理

#### RDR_LoadReaderDrivers
```c
int RDR_LoadReaderDrivers(const char* drvpath)
```
**功能**: 加载RFID阅读器驱动程序  
**参数**: 
- `drvpath`: 驱动程序目录路径（如"./Drivers"）

**返回值**: 
- `0`: 成功
- `非0`: 失败错误码

**说明**: 
- 必须在打开设备前调用
- 加载Drivers目录中的所有驱动DLL文件
- 支持LSG405、LSG606等多种型号设备

**使用示例**:
```c
int ret = RDR_LoadReaderDrivers("./Drivers");
if (ret != 0) {
    printf("加载驱动失败，错误码: %d\n", ret);
}
```

#### RDR_Open
```c
int RDR_Open(const char* connStr, UIntPtr* hrOut)
```
**功能**: 打开RFID阅读器连接  
**参数**: 
- `connStr`: 连接字符串
- `hrOut`: 输出参数，返回设备句柄

**连接字符串格式**:
```
"RDType=LSG405;CommType=NET;RemoteIP=*************;RemotePort=6012;LocalIP="
```

**参数说明**:
- `RDType`: 设备类型（LSG405、LSG606）
- `CommType`: 通信类型（NET=网络通信）
- `RemoteIP`: 设备IP地址
- `RemotePort`: 设备端口（默认6012）
- `LocalIP`: 本地IP地址（可为空）

**返回值**: 
- `0`: 成功，hrOut包含有效设备句柄
- `非0`: 失败错误码

**使用示例**:
```c
UIntPtr hReader;
const char* connStr = "RDType=LSG405;CommType=NET;RemoteIP=*************;RemotePort=6012;LocalIP=";
int ret = RDR_Open(connStr, &hReader);
if (ret == 0) {
    printf("设备连接成功，句柄: %p\n", (void*)hReader);
} else {
    printf("设备连接失败，错误码: %d\n", ret);
}
```

#### RDR_Close
```c
int RDR_Close(UIntPtr hr)
```
**功能**: 关闭RFID阅读器连接  
**参数**: 
- `hr`: 设备句柄（由RDR_Open返回）

**返回值**: 
- `0`: 成功
- `非0`: 失败错误码

### 2. RFID标签操作

#### RDR_CreateInvenParamSpecList
```c
UIntPtr RDR_CreateInvenParamSpecList()
```
**功能**: 创建库存扫描参数列表  
**参数**: 无  
**返回值**: 参数列表句柄  
**说明**: 用于配置库存扫描的参数，如天线功率、扫描时间等

#### RDR_TagInventory
```c
int RDR_TagInventory(UIntPtr hr, Byte AIType, Byte AntennaCount, Byte* AntennaIDs, UIntPtr InvenParamSpecList)
```
**功能**: 执行RFID标签库存扫描  
**参数**: 
- `hr`: 设备句柄
- `AIType`: 空中接口类型
  - `1`: ISO15693（高频13.56MHz）
  - `2`: ISO18000-6C（超高频）
- `AntennaCount`: 天线数量
- `AntennaIDs`: 天线ID数组
- `InvenParamSpecList`: 库存参数列表句柄

**返回值**: 
- `0`: 成功
- `非0`: 失败错误码

**使用示例**:
```c
UIntPtr paramList = RDR_CreateInvenParamSpecList();
Byte antennas[] = {1}; // 使用天线1
int ret = RDR_TagInventory(hReader, 1, 1, antennas, paramList);
```

#### RDR_GetTagDataReportCount
```c
UInt32 RDR_GetTagDataReportCount(UIntPtr hr)
```
**功能**: 获取标签数据报告数量  
**参数**: 
- `hr`: 设备句柄

**返回值**: 报告数量

#### RDR_GetTagDataReport
```c
UIntPtr RDR_GetTagDataReport(UIntPtr hr, Byte seek)
```
**功能**: 获取标签数据报告  
**参数**: 
- `hr`: 设备句柄
- `seek`: 查找方式
  - `1`: RFID_SEEK_FIRST（第一个）
  - `2`: RFID_SEEK_NEXT（下一个）

**返回值**: 报告句柄（0表示无更多报告）

#### RDR_ParseTagDataReportRaw
```c
int RDR_ParseTagDataReportRaw(UIntPtr hTagReport, Byte* rawBuffer, UInt32* nSize)
```
**功能**: 解析标签数据报告的原始数据  
**参数**: 
- `hTagReport`: 报告句柄
- `rawBuffer`: 输出缓冲区
- `nSize`: 缓冲区大小（输入输出参数）

**返回值**: 
- `0`: 成功
- `非0`: 失败错误码

**数据格式**:
```
Byte[0]   = 事件类型
Byte[1]   = 方向（0=进入，1=离开）
Byte[2-7] = 时间戳（年、月、日、时、分、秒）
Byte[8]   = 标签数据长度
Byte[9+]  = 标签UID数据
```

**完整扫描示例**:
```c
// 执行库存扫描
UIntPtr paramList = RDR_CreateInvenParamSpecList();
Byte antennas[] = {1};
int ret = RDR_TagInventory(hReader, 1, 1, antennas, paramList);

if (ret == 0) {
    // 获取报告数量
    UInt32 reportCount = RDR_GetTagDataReportCount(hReader);
    printf("发现 %d 个标签报告\n", reportCount);
    
    // 获取第一个报告
    UIntPtr hReport = RDR_GetTagDataReport(hReader, 1);
    
    while (hReport != 0) {
        // 解析报告数据
        Byte rawBuffer[64];
        UInt32 bufferSize = sizeof(rawBuffer);
        
        ret = RDR_ParseTagDataReportRaw(hReport, rawBuffer, &bufferSize);
        if (ret == 0 && bufferSize >= 9) {
            Byte eventType = rawBuffer[0];
            Byte direction = rawBuffer[1];
            Byte dataLen = rawBuffer[8];
            
            printf("事件类型: %d, 方向: %s\n", eventType, direction ? "离开" : "进入");
            
            // 提取标签UID
            if (bufferSize >= 9 + dataLen) {
                printf("标签UID: ");
                for (int i = 0; i < dataLen; i++) {
                    printf("%02X", rawBuffer[9 + i]);
                }
                printf("\n");
            }
        }
        
        // 获取下一个报告
        hReport = RDR_GetTagDataReport(hReader, 2);
    }
}
```

### 3. 通行统计管理

#### RDR_GetPassingCounter
```c
int RDR_GetPassingCounter(UIntPtr hr, UInt32* inFlow, UInt32* outFlow)
```
**功能**: 获取通行计数  
**参数**: 
- `hr`: 设备句柄
- `inFlow`: 输出参数，进入人数
- `outFlow`: 输出参数，离开人数

**返回值**: 
- `0`: 成功
- `非0`: 失败错误码

**使用示例**:
```c
UInt32 inFlow, outFlow;
int ret = RDR_GetPassingCounter(hReader, &inFlow, &outFlow);
if (ret == 0) {
    printf("进入人数: %u, 离开人数: %u\n", inFlow, outFlow);
}
```

#### RDR_ResetPassingCounter
```c
int RDR_ResetPassingCounter(UIntPtr hr, UInt32 flag)
```
**功能**: 重置通行计数  
**参数**: 
- `hr`: 设备句柄
- `flag`: 重置标志
  - `1`: 重置进入计数
  - `2`: 重置离开计数
  - `3`: 重置所有计数

**返回值**: 
- `0`: 成功
- `非0`: 失败错误码

#### RDR_SetPassingCounter
```c
int RDR_SetPassingCounter(UIntPtr hr, Byte addr, UInt32 inFlow, UInt32 outFlow)
```
**功能**: 设置通行计数  
**参数**: 
- `hr`: 设备句柄
- `addr`: 地址（通常为1）
- `inFlow`: 进入人数
- `outFlow`: 离开人数

**返回值**: 
- `0`: 成功
- `非0`: 失败错误码

#### RDR_ReverseInOutDirection
```c
int RDR_ReverseInOutDirection(UIntPtr hr)
```
**功能**: 反转进出方向  
**参数**: 
- `hr`: 设备句柄

**返回值**: 
- `0`: 成功
- `非0`: 失败错误码

**说明**: 交换进入和离开的方向定义，用于设备安装方向调整

### 4. 系统时间管理

#### RDR_GetSystemTime
```c
int RDR_GetSystemTime(UIntPtr hr, UInt32* year, Byte* month, Byte* day, Byte* hour, Byte* minute, Byte* second)
```
**功能**: 获取设备系统时间  
**参数**: 
- `hr`: 设备句柄
- `year`: 输出参数，年份
- `month`: 输出参数，月份（1-12）
- `day`: 输出参数，日期（1-31）
- `hour`: 输出参数，小时（0-23）
- `minute`: 输出参数，分钟（0-59）
- `second`: 输出参数，秒（0-59）

**返回值**: 
- `0`: 成功
- `非0`: 失败错误码

#### RDR_SetSystemTime
```c
int RDR_SetSystemTime(UIntPtr hr, UInt32 year, Byte month, Byte day, Byte hour, Byte minute, Byte second)
```
**功能**: 设置设备系统时间  
**参数**: 
- `hr`: 设备句柄
- `year`: 年份
- `month`: 月份（1-12）
- `day`: 日期（1-31）
- `hour`: 小时（0-23）
- `minute`: 分钟（0-59）
- `second`: 秒（0-59）

**返回值**: 
- `0`: 成功
- `非0`: 失败错误码

**时间管理示例**:
```c
// 获取设备时间
UInt32 year;
Byte month, day, hour, minute, second;
int ret = RDR_GetSystemTime(hReader, &year, &month, &day, &hour, &minute, &second);
if (ret == 0) {
    printf("设备时间: %04u-%02u-%02u %02u:%02u:%02u\n", 
           year, month, day, hour, minute, second);
}

// 设置设备时间为当前时间
time_t now = time(NULL);
struct tm* timeinfo = localtime(&now);
ret = RDR_SetSystemTime(hReader, 
                        timeinfo->tm_year + 1900,
                        timeinfo->tm_mon + 1,
                        timeinfo->tm_mday,
                        timeinfo->tm_hour,
                        timeinfo->tm_min,
                        timeinfo->tm_sec);
```

### 5. 数据管理

#### RDR_BuffMode_FetchRecords
```c
int RDR_BuffMode_FetchRecords(UIntPtr hr, UInt32 flags)
```
**功能**: 获取缓冲模式记录  
**参数**: 
- `hr`: 设备句柄
- `flags`: 标志位

**返回值**: 
- `0`: 成功
- `非0`: 失败错误码

**说明**: 从设备缓冲区获取历史记录，用于离线数据同步

#### RDR_FetchTodayPassingData
```c
int RDR_FetchTodayPassingData(UIntPtr hr, Byte nFlag, Byte nAddr)
```
**功能**: 获取今日通行数据  
**参数**: 
- `hr`: 设备句柄
- `nFlag`: 标志
- `nAddr`: 地址

**返回值**: 
- `0`: 成功
- `非0`: 失败错误码

#### RDR_FetchHistoryPassingData
```c
int RDR_FetchHistoryPassingData(UIntPtr hr, Byte nFlag, Byte nAddr)
```
**功能**: 获取历史通行数据  
**参数**: 
- `hr`: 设备句柄
- `nFlag`: 标志
- `nAddr`: 地址

**返回值**: 
- `0`: 成功
- `非0`: 失败错误码

#### RDR_GetTodayPassingReport
```c
UIntPtr RDR_GetTodayPassingReport(UIntPtr hr, Byte seek)
```
**功能**: 获取今日通行报告  
**参数**: 
- `hr`: 设备句柄
- `seek`: 查找方式（1=第一个，2=下一个）

**返回值**: 报告句柄

#### RDR_ParseTodayPassingReport
```c
int RDR_ParseTodayPassingReport(UIntPtr hReport, Byte* mDirection, UInt32* mPassingValue, Byte* mTime)
```
**功能**: 解析今日通行报告  
**参数**: 
- `hReport`: 报告句柄
- `mDirection`: 输出参数，方向
- `mPassingValue`: 输出参数，通行值
- `mTime`: 输出参数，时间数组

**返回值**: 
- `0`: 成功
- `非0`: 失败错误码

### 6. 传感器监控

#### LSG_GetIRSta
```c
int LSG_GetIRSta(UIntPtr hr, byte sid, UInt32* ir_tri_cnt, byte* ir_num)
```
**功能**: 获取红外传感器状态  
**参数**: 
- `hr`: 设备句柄
- `sid`: 传感器ID（通常为0，表示所有传感器）
- `ir_tri_cnt`: 输出数组，各传感器触发计数
- `ir_num`: 输入输出参数，传感器数量

**返回值**: 
- `0`: 成功
- `非0`: 失败错误码

**说明**: 
- LSGate特有方法，用于监控红外传感器状态
- 可用于检测通道中是否有人员或物体
- 触发计数可用于统计和故障诊断

**使用示例**:
```c
UInt32 irCounts[255];
byte irNum = 255;
int ret = LSG_GetIRSta(hReader, 0, irCounts, &irNum);
if (ret == 0) {
    printf("红外传感器数量: %d\n", irNum);
    for (int i = 0; i < irNum; i++) {
        printf("传感器%d触发次数: %u\n", i, irCounts[i]);
    }
}
```

### 7. 设备信息

#### RDR_GetReaderInfor
```c
int RDR_GetReaderInfor(UIntPtr hr, Byte Type, char* buffer, UInt32* nSize)
```
**功能**: 获取阅读器信息  
**参数**: 
- `hr`: 设备句柄
- `Type`: 信息类型
  - `0`: 基本设备信息
  - `1`: 固件版本
  - `2`: 硬件版本
- `buffer`: 输出缓冲区
- `nSize`: 缓冲区大小

**返回值**: 
- `0`: 成功
- `非0`: 失败错误码

#### RDR_GetLibVersion
```c
UInt32 RDR_GetLibVersion(char* buf, UInt32 nSize)
```
**功能**: 获取库版本信息  
**参数**: 
- `buf`: 输出缓冲区
- `nSize`: 缓冲区大小

**返回值**: 实际字符串长度

## 🔄 典型使用流程

### 基本设备操作流程
```c
#include <stdio.h>
#include <stdlib.h>

int main() {
    UIntPtr hReader;
    int ret;
    
    // 1. 加载驱动
    ret = RDR_LoadReaderDrivers("./Drivers");
    if (ret != 0) {
        printf("加载驱动失败: %d\n", ret);
        return -1;
    }
    
    // 2. 打开设备
    const char* connStr = "RDType=LSG405;CommType=NET;RemoteIP=*************;RemotePort=6012;LocalIP=";
    ret = RDR_Open(connStr, &hReader);
    if (ret != 0) {
        printf("设备连接失败: %d\n", ret);
        return -1;
    }
    
    printf("设备连接成功\n");
    
    // 3. 获取设备信息
    char deviceInfo[256];
    UInt32 infoSize = sizeof(deviceInfo);
    ret = RDR_GetReaderInfor(hReader, 0, deviceInfo, &infoSize);
    if (ret == 0) {
        printf("设备信息: %s\n", deviceInfo);
    }
    
    // 4. 获取通行统计
    UInt32 inFlow, outFlow;
    ret = RDR_GetPassingCounter(hReader, &inFlow, &outFlow);
    if (ret == 0) {
        printf("通行统计 - 进入: %u, 离开: %u\n", inFlow, outFlow);
    }
    
    // 5. 执行RFID扫描
    UIntPtr paramList = RDR_CreateInvenParamSpecList();
    Byte antennas[] = {1};
    ret = RDR_TagInventory(hReader, 1, 1, antennas, paramList);
    if (ret == 0) {
        UInt32 reportCount = RDR_GetTagDataReportCount(hReader);
        printf("发现 %u 个标签\n", reportCount);
        
        // 处理标签数据...
    }
    
    // 6. 关闭设备
    RDR_Close(hReader);
    printf("设备已关闭\n");
    
    return 0;
}
```

### 实时监控流程
```c
void realtime_monitoring(UIntPtr hReader) {
    while (1) {
        // 执行库存扫描
        UIntPtr paramList = RDR_CreateInvenParamSpecList();
        Byte antennas[] = {1};
        int ret = RDR_TagInventory(hReader, 1, 1, antennas, paramList);
        
        if (ret == 0) {
            UInt32 reportCount = RDR_GetTagDataReportCount(hReader);
            
            if (reportCount > 0) {
                UIntPtr hReport = RDR_GetTagDataReport(hReader, 1);
                
                while (hReport != 0) {
                    Byte rawBuffer[64];
                    UInt32 bufferSize = sizeof(rawBuffer);
                    
                    ret = RDR_ParseTagDataReportRaw(hReport, rawBuffer, &bufferSize);
                    if (ret == 0 && bufferSize >= 9) {
                        Byte direction = rawBuffer[1];
                        Byte dataLen = rawBuffer[8];
                        
                        printf("检测到%s事件\n", direction ? "离开" : "进入");
                        
                        // 处理标签数据
                        if (bufferSize >= 9 + dataLen) {
                            printf("标签UID: ");
                            for (int i = 0; i < dataLen; i++) {
                                printf("%02X", rawBuffer[9 + i]);
                            }
                            printf("\n");
                        }
                    }
                    
                    hReport = RDR_GetTagDataReport(hReader, 2);
                }
            }
        }
        
        // 检查红外传感器状态
        UInt32 irCounts[10];
        Byte irNum = 10;
        ret = LSG_GetIRSta(hReader, 0, irCounts, &irNum);
        if (ret == 0) {
            for (int i = 0; i < irNum; i++) {
                if (irCounts[i] > 0) {
                    printf("红外传感器%d活跃\n", i);
                }
            }
        }
        
        // 延迟100ms
        usleep(100000);
    }
}
```

## ⚠️ 错误码说明

| 错误码 | 含义 | 解决方案 |
|--------|------|----------|
| 0 | 成功 | - |
| -1 | 一般错误 | 检查参数和设备状态 |
| -2 | 设备未连接 | 检查网络连接和IP地址 |
| -3 | 参数错误 | 检查传入参数的有效性 |
| -4 | 超时 | 检查网络延迟和设备响应 |
| -5 | 设备忙 | 等待设备完成当前操作 |
| -10 | 驱动加载失败 | 检查Drivers目录和DLL文件 |
| -11 | 设备类型不支持 | 检查连接字符串中的RDType |
| -12 | 网络连接失败 | 检查IP地址、端口和网络连通性 |

## 🔧 故障排除

### 1. 连接问题
- **现象**: RDR_Open返回非0错误码
- **检查项**:
  - 设备IP地址是否正确
  - 网络是否连通（ping测试）
  - 端口6012是否被占用
  - 防火墙是否阻止连接

### 2. 驱动问题
- **现象**: RDR_LoadReaderDrivers失败
- **检查项**:
  - Drivers目录是否存在
  - DLL文件是否完整
  - 系统是否缺少运行库（VC++ Redistributable）

### 3. 扫描问题
- **现象**: 无法检测到RFID标签
- **检查项**:
  - 标签是否为ISO15693高频标签
  - 标签是否在读取范围内
  - 天线是否正常工作
  - 设备是否正确配置

### 4. 性能问题
- **现象**: 响应缓慢或超时
- **检查项**:
  - 网络延迟是否过高
  - 设备负载是否过重
  - 扫描频率是否过高
  - 缓冲区是否溢出

## 📝 注意事项

1. **线程安全**: SDK不是线程安全的，多线程使用时需要加锁
2. **内存管理**: 及时释放创建的参数列表和报告句柄
3. **网络稳定性**: 建议使用有线网络连接，避免WiFi不稳定
4. **设备独占**: 同一时间只能有一个应用连接设备
5. **错误处理**: 所有API调用都应检查返回值
6. **资源清理**: 程序退出前必须调用RDR_Close关闭设备

## 📞 技术支持

如遇到技术问题，请提供以下信息：
- 设备型号和固件版本
- 错误码和错误现象
- 网络配置信息
- 相关日志文件

---

**版本**: 1.0  
**更新日期**: 2024年  
**适用设备**: LSG405、LSG606系列图书馆安全门RFID阅读器