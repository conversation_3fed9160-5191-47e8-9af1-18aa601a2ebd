# 软件监控脚本配置向导
# 帮助用户快速配置监控脚本

Write-Host "==================== 软件监控脚本配置向导 ====================" -ForegroundColor Green
Write-Host ""

# 获取软件exe文件路径
do {
    Write-Host "请选择你的软件exe文件:" -ForegroundColor Yellow
    Add-Type -AssemblyName System.Windows.Forms
    $OpenFileDialog = New-Object System.Windows.Forms.OpenFileDialog
    $OpenFileDialog.Filter = "可执行文件 (*.exe)|*.exe"
    $OpenFileDialog.Title = "选择你的软件exe文件"
    
    if ($OpenFileDialog.ShowDialog() -eq [System.Windows.Forms.DialogResult]::OK) {
        $ExecutablePath = $OpenFileDialog.FileName
        $WorkingDirectory = Split-Path $ExecutablePath -Parent
        $ProcessName = [System.IO.Path]::GetFileNameWithoutExtension($ExecutablePath)
        
        Write-Host "已选择软件: $ExecutablePath" -ForegroundColor Green
        Write-Host "进程名: $ProcessName" -ForegroundColor Green
        Write-Host "工作目录: $WorkingDirectory" -ForegroundColor Green
        Write-Host ""
        
        $Confirm = Read-Host "确认这是正确的软件吗？(y/n)"
    } else {
        Write-Host "未选择文件，退出配置。" -ForegroundColor Red
        exit
    }
} while ($Confirm -ne "y" -and $Confirm -ne "Y")

# 设置日志文件路径
$LogFile = Join-Path $WorkingDirectory "Logs\AppMonitor.log"
Write-Host "日志文件将保存到: $LogFile" -ForegroundColor Cyan

# 设置检查间隔
do {
    $IntervalInput = Read-Host "请输入检查间隔（秒，默认10秒，直接回车使用默认值）"
    if ([string]::IsNullOrWhiteSpace($IntervalInput)) {
        $CheckInterval = 10
        break
    } elseif ([int]::TryParse($IntervalInput, [ref]$CheckInterval) -and $CheckInterval -gt 0) {
        break
    } else {
        Write-Host "请输入有效的正整数！" -ForegroundColor Red
    }
} while ($true)

Write-Host "检查间隔设置为: $CheckInterval 秒" -ForegroundColor Green

# 生成配置内容
$ConfigContent = @"
# ==================== 配置区域 ====================
# 请根据你的软件修改以下配置
`$ProcessName = "$ProcessName"             # 你的软件进程名（不包含.exe）
`$ExecutablePath = "$ExecutablePath"       # 你的软件完整路径
`$WorkingDirectory = "$WorkingDirectory"               # 软件工作目录（软件所在文件夹）
`$StartupArguments = ""             # 启动参数（绿色软件通常不需要）
`$CheckInterval = $CheckInterval                # 检查间隔（秒）
`$LogFile = "$LogFile"   # 日志文件路径（建议放在软件目录下）
# ==================== 配置区域结束 ====================
"@

# 读取原始脚本
$ScriptPath = Join-Path $PSScriptRoot "软件监控脚本.ps1"
if (!(Test-Path $ScriptPath)) {
    Write-Host "错误: 找不到 软件监控脚本.ps1 文件！" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit
}

$OriginalContent = Get-Content $ScriptPath -Raw

# 替换配置区域
$Pattern = '# ==================== 配置区域 ====================.*?# ==================== 配置区域结束 ===================='
$NewContent = $OriginalContent -replace $Pattern, $ConfigContent, 'Singleline'

# 保存新配置
Set-Content -Path $ScriptPath -Value $NewContent -Encoding UTF8

Write-Host ""
Write-Host "==================== 配置完成 ====================" -ForegroundColor Green
Write-Host "配置已保存到: $ScriptPath" -ForegroundColor Green
Write-Host ""
Write-Host "下一步操作:" -ForegroundColor Yellow
Write-Host "1. 运行 '启动监控脚本.bat' 测试脚本是否正常工作" -ForegroundColor White
Write-Host "2. 如果测试正常，可以设置开机自启动" -ForegroundColor White
Write-Host "3. 查看 '设置说明.md' 了解如何设置开机自启动" -ForegroundColor White
Write-Host ""

$TestNow = Read-Host "是否现在测试脚本？(y/n)"
if ($TestNow -eq "y" -or $TestNow -eq "Y") {
    Write-Host "正在启动监控脚本..." -ForegroundColor Green
    $BatPath = Join-Path $PSScriptRoot "启动监控脚本.bat"
    if (Test-Path $BatPath) {
        Start-Process -FilePath $BatPath -Verb RunAs
    } else {
        Write-Host "找不到 启动监控脚本.bat 文件！" -ForegroundColor Red
    }
}

Read-Host "按任意键退出"
