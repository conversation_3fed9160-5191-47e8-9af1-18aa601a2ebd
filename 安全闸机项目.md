# 安全闸机项目

## 项目背景

本项目旨在开发一款集成了人脸识别、二维码扫描、IC卡读取等多种认证方式的智能门禁系统。系统将部署在特定的硬件终端上，用于提供安全、高效的人员身份验证，并根据验证结果执行相应操作（如开启门禁）和记录事件（如考勤）。

## 项目目标

1.  实现人脸识别、二维码扫描、IC卡读取等多种认证方式
2.  根据验证结果执行相应操作（如开启门禁）
3.  记录认证事件（考勤记录）


AA 00 64 80 00 00 06 D2        进馆开始流程
AA 00 65 80 00 00 07 2E        进馆结束流程

AA 00 C8 80 00 00 27 82        出馆开始流程
AA 00 C9 80 00 00 26 7E        出馆结束流程

AA 00 0A 80 00 00              到达指定位置

AA 00 02 01 00 00 48 72        进馆开门
AA 00 01 01 00 00 48 36        出馆开门
AA 00 02 01 00 00 48 72        失败信号

AA 00 0F 80 00 00              尾随
AA 00 0B 80 00 00              开门有人

首先 这些命令是和硬件交互， 硬件会通过串口和电脑主机进行消息通信。

## 场景1  读者进入图书馆操作

1. 用户来到闸机进口处时，闸机会通过串口向电脑主机发送命令  进馆开始流程命令
2. 用户会拿着读者证或者二维码 进行认证，随后显示认证结果并且 发送  进馆开门命令
3. 当用户通过闸机通道时，闸机发送 进馆结束流程命令


## 场景2  读者离开图书馆操作

1. 用户来到闸机出口处时，闸机会通过串口向电脑主机发送命令  出馆开始流程命令
2. 程序接收到 出馆开始流程命令 后，就立即打开 rfid阅读器 进行扫描，，当用户移动到某一个位置时，，
闸机会发送 到达指定位置命令，程序接收到 到达指定位置命令 后，就延迟1秒或者立即关闭rfid阅读器，，，
这个阶段将陆续扫描到的条码保存起来，请求一个接口，把书籍信息以列表的形式 显示在屏幕上，
如  条码   书籍名称    状态    状态 是已借 和 未借   ，，，已借  整条记录为绿色  未借   整条记录未红色
判断这些书籍是否借出，
3. 如果 没有扫描到书籍 或者扫描到的书籍 都是已借状态，，那么  就发送 出馆开门命令，否则 在页面提示   检测到未借书籍，请退出通道去处理
4. 当用户通过闸机通道时，闸机发送 出馆结束流程命令

进馆流程和出馆流程 是 不同的流程  要合理处理


enum GateState {
  idle,             // 空闲
  enterStarted,      // 进馆开始
  enterScanning,    // 进馆扫描中
  enterOpening,     // 进馆开门中
  enterOver,        // 进馆结束
  exitStarted,      // 出馆开始
  exitScanning,     // 出馆RFID扫描中
  exitChecking,     // 出馆书籍检查中
  exitOver,         // 出馆结束
  error             // 异常状态
}


操作 rfid阅读器 进行扫描  是在另一个插件项目中实现的，，在本项目引入后调用即可，，当前先待定 

现在这种情况   安全闸机 通道1 使用电脑主机1   使用rfid阅读器1 ，   通道2，使用电脑主机2，使用rfid阅读器1       电脑主机1 和电脑主机2  使用网口和  rfid阅读器1 连接，，，这种情况  如何处理两个通道的情况？