@echo off
title cursor_star Monitor (Fixed)

REM ==================== Configuration ====================
set "PROCESS_NAME=cursor_star.exe"
set "EXE_PATH=C:\Users\<USER>\Desktop\Release\cursor_star.exe"
set "WORK_DIR=C:\Users\<USER>\Desktop\Release"
set "CHECK_INTERVAL=10"
REM ==================== Configuration End ====================

echo ==================== cursor_star Monitor Started ====================
echo Process Name: %PROCESS_NAME%
echo Executable Path: %EXE_PATH%
echo Working Directory: %WORK_DIR%
echo Check Interval: %CHECK_INTERVAL% seconds
echo ================================================================

REM Check if executable exists
if not exist "%EXE_PATH%" (
    echo ERROR: Cannot find executable file %EXE_PATH%
    pause
    exit /b 1
)

REM Check if working directory exists
if not exist "%WORK_DIR%" (
    echo ERROR: Cannot find working directory %WORK_DIR%
    pause
    exit /b 1
)

echo Starting monitor...
echo.

:MONITOR_LOOP
    REM Get current time
    for /f "tokens=1-3 delims=:." %%a in ("%time%") do (
        set "current_time=%%a:%%b:%%c"
    )
    
    REM Check if process is running
    tasklist /FI "IMAGENAME eq %PROCESS_NAME%" 2>NUL | find /I "%PROCESS_NAME%" >NUL
    
    if %ERRORLEVEL% EQU 0 (
        echo [%current_time%] Software is running
    ) else (
        echo [%current_time%] Software not running, starting...

        REM Change to working directory and start
        cd /d "%WORK_DIR%"
        start "" "%EXE_PATH%"

        if %ERRORLEVEL% EQU 0 (
            echo [%current_time%] Software started successfully in directory: %WORK_DIR%
        ) else (
            echo [%current_time%] Failed to start software
        )
    )
    
    REM Wait for specified seconds
    timeout /t %CHECK_INTERVAL% /nobreak >nul
    
    goto MONITOR_LOOP
