# 🚀 LSGate图书馆安全门RFID阅读器在a3g项目中的使用指南

## ✅ 完成状态确认

**LSGate图书馆安全门RFID阅读器现在已经可以在a3g项目中正常使用了！**

## 📋 已完成的集成工作

### 1. **Hardware包更新** ✅
- ✅ 添加了`HWLSGateInfoData`数据模型
- ✅ 添加了`LSGateBridge`、`LSGateWindowsBridge`、`LSGateLinuxBridge`
- ✅ 在`SEBridgeList.dart`中注册了LSGate（ID: 22）
- ✅ 在`HWReaderSettingData.dart`中添加了LSGate支持

### 2. **a3g项目更新** ✅
- ✅ 更新了`enhanced_rfid_service.dart`以支持LSGate网口配置检测
- ✅ 添加了LSGate测试页面`lsgate_test_page.dart`
- ✅ 更新了依赖包（`flutter pub get`）

## 🎯 使用方式

### 方式1: 在seasetting中配置（推荐）

1. **打开seasetting配置界面**
2. **选择"图书馆安全门RFID阅读器"**
3. **配置参数**：
   ```
   解码器:        UTF8
   串口号:        COM3
   波特率:        9600
   网口IP地址:    *************  (留空使用串口)
   网口端口号:    6012
   设备类型:      LSG405
   天线配置:      1
   ```
4. **保存配置**

### 方式2: 直接在代码中使用

```dart
// 创建LSGate配置
HWReaderSettingData lsgateConfig = HWReaderSettingData(
  readerType: 22, // LSGate阅读器类型ID
  info: HWLSGateInfoData(
    decoderType: "UTF8",
    ipAddress: "*************",    // 网口IP地址
    netPort: "6012",               // 网口端口
    comPort: "COM3",               // 串口备用
    baud: "9600",                  // 波特率
    deviceType: "LSG405",          // 设备类型
    inAnt: "1",                    // 天线配置
  ),
  selectedCardType: "高频",
  coderConfig: HWCoderConfigData(
    bookCode: "30",
    shelfCode: "31",
    readerCardCode: "32",
    cdCode: "33",
    prefixStr: "",
    suffixStr: "",
    replaceRuleStr: "",
  ),
);

// 配置阅读器
await ReaderManager.instance.changeReaders(
  jsonEncode([lsgateConfig.toJson()])
);

// 启动阅读器
ReaderManager.instance.open();
ReaderManager.instance.startInventory();

// 监听扫描结果
context.read<HWTagProvider>().addListener(() {
  final tagProvider = context.read<HWTagProvider>();
  for (HWTagData tag in tagProvider.tagList) {
    print('扫描到标签: ${tag.barCode ?? tag.uid}');
  }
});
```

### 方式3: 使用LSGate特有功能

```dart
// 获取通行统计
Map<String, int> counter = LSGateBridge.instance.getPassingCounter();
print('进馆人数: ${counter['inFlow']}, 出馆人数: ${counter['outFlow']}');

// 获取红外传感器状态
Map<String, dynamic> irStatus = LSGateBridge.instance.getIRStatus();
print('红外传感器状态: $irStatus');

// 重置通行统计
LSGateBridge.instance.resetPassingCounter();
```

## 🧪 测试方法

### 1. **运行LSGate测试页面**

```dart
// 在a3g项目中导航到测试页面
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const LSGateTestPage(),
  ),
);
```

### 2. **在安全闸机中自动使用**

LSGate阅读器会在安全闸机的出馆流程中自动使用，无需额外配置。系统会：

1. **自动检测配置**: 如果配置了LSGate阅读器（readerType=22），系统会自动使用
2. **智能连接**: 优先使用网口连接，串口备用
3. **实时扫描**: 在出馆流程中自动启动RFID扫描
4. **书籍信息获取**: 自动获取扫描到的书籍详细信息
5. **开门决策**: 基于书籍借阅状态决定是否开门

## 🔧 配置说明

### 网络连接配置（推荐）
```dart
HWLSGateInfoData(
  ipAddress: "*************",  // 设备IP地址
  netPort: "6012",             // 设备端口
  deviceType: "LSG405",        // 设备型号
)
```

### 串口连接配置（备用）
```dart
HWLSGateInfoData(
  ipAddress: "",               // 留空使用串口
  comPort: "COM3",             // 串口号
  baud: "9600",                // 波特率
  deviceType: "LSG405",        // 设备型号
)
```

## 🎨 在安全闸机中的集成

### enhanced_rfid_service.dart中的自动支持

系统已经在`enhanced_rfid_service.dart`中添加了LSGate支持：

```dart
/// 🔥 借鉴：网口配置检测（支持LSGate和UHF阅读器）
Map<String, String>? _findNetworkConfig() {
  for (HWReaderSettingData config in _readerConfigs) {
    // 检查LSGate图书馆安全门RFID阅读器
    if (config.info is HWLSGateInfoData) {
      HWLSGateInfoData info = config.info as HWLSGateInfoData;
      String? ipAddress = info.valueForKey('ipAddress');
      String? netPort = info.valueForKey('netPort');
      
      if (ipAddress?.isNotEmpty == true && netPort?.isNotEmpty == true) {
        return {
          'ipAddress': ipAddress!,
          'port': netPort!,
          'readerType': 'LSGate图书馆安全门RFID阅读器',
        };
      }
    }
  }
  return null;
}
```

## 🚀 立即可用

**现在您可以立即在a3g项目中使用LSGate图书馆安全门RFID阅读器了！**

### 快速验证步骤：

1. **启动a3g项目**
2. **打开seasetting配置界面**
3. **添加"图书馆安全门RFID阅读器"配置**
4. **配置网口IP地址和端口**
5. **保存配置**
6. **在安全闸机中测试出馆流程**

### 预期结果：

- ✅ 系统会自动识别LSGate配置
- ✅ 优先使用网口连接
- ✅ 在出馆流程中自动启动RFID扫描
- ✅ 实时显示扫描到的书籍信息
- ✅ 基于借阅状态决定开门

## 📞 技术支持

如果在使用过程中遇到问题，请检查：

1. **网络连接**: 确保设备IP地址可达
2. **端口配置**: 确保端口号正确
3. **驱动文件**: 确保SDK文件完整
4. **配置格式**: 确保配置参数正确

**LSGate图书馆安全门RFID阅读器现在已经完全集成到a3g项目中，可以立即使用！** 🎉