import 'book_info.dart';

/// 书籍扫描状态枚举
enum BookScanStatus {
  processing,  // 处理中
  success,     // 成功
  failed,      // 失败
  notFound,    // 未找到（其他馆的条码）
}

/// 书籍扫描结果模型
class BookScanResult {
  final String barcode;
  final BookInfo? bookInfo;
  final DateTime scanTime;
  final BookScanStatus status;
  final String? error;

  BookScanResult({
    required this.barcode,
    this.bookInfo,
    required this.scanTime,
    required this.status,
    this.error,
  });

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'barcode': barcode,
      'bookInfo': bookInfo?.toJson(),
      'scanTime': scanTime.toIso8601String(),
      'status': status.name,
      'error': error,
    };
  }

  /// 从JSON创建
  factory BookScanResult.fromJson(Map<String, dynamic> json) {
    return BookScanResult(
      barcode: json['barcode'] ?? '',
      bookInfo: json['bookInfo'] != null ? BookInfo.fromJson(json['bookInfo']) : null,
      scanTime: DateTime.parse(json['scanTime'] ?? DateTime.now().toIso8601String()),
      status: BookScanStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => BookScanStatus.failed,
      ),
      error: json['error'],
    );
  }

  @override
  String toString() {
    return 'BookScanResult(barcode: $barcode, status: $status, bookInfo: ${bookInfo?.bookName}, error: $error)';
  }
}
