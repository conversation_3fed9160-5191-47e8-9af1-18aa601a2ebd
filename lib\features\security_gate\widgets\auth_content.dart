import 'package:flutter/material.dart';
import 'package:a3g/core/utils/window_util.dart';
import '../../auth/services/multi_auth_manager.dart';
import '../config/gate_auth_config.dart';

/// 极简认证界面内容组件
/// 专为安全闸机设计，只显示基本的认证提示信息
class AuthContent extends StatelessWidget {
  const AuthContent({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // 极简认证图标
        Container(
          width: 120.p,
          height: 120.p,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.blue.withOpacity(0.1),
            border: Border.all(
              color: Colors.blue,
              width: 2,
            ),
          ),
          child: Icon(
            Icons.credit_card,
            size: 60.p,
            color: Colors.blue,
          ),
        ),

        SizedBox(height: 40.p),

        // 简单提示文字
        Text(
          '请进行身份认证',
          style: TextStyle(
            color: Colors.white,
            fontSize: 32.p,
            fontWeight: FontWeight.w500,
          ),
        ),

        SizedBox(height: 20.p),

        // 认证方式提示 - 动态显示配置的认证方式
        _buildAuthMethodsText(),
      ],
    );
  }

  /// 构建认证方式提示文字
  Widget _buildAuthMethodsText() {
    try {
      // 获取MultiAuthManager实例
      final multiAuthManager = MultiAuthManager.instance;
      final enabledMethods = multiAuthManager.enabledMethods;

      // 如果没有启用的认证方式，显示默认文字
      if (enabledMethods.isEmpty) {
        return Text(
          '支持读者证、微信扫码、人脸识别',
          style: TextStyle(
            color: Colors.white.withOpacity(0.8),
            fontSize: 24.p,
          ),
        );
      }

      // 过滤出适合闸机的认证方式
      final gateSuitableMethods = enabledMethods
          .where((method) => GateAuthConfig.isMethodSuitableForGate(method))
          .toList();

      // 如果没有适合闸机的认证方式，使用默认配置
      if (gateSuitableMethods.isEmpty) {
        final defaultMethods = GateAuthConfig.defaultEnabledMethods
            .map((method) => GateAuthConfig.getAuthMethodDisplayName(method))
            .join('、');

        return Text(
          '支持$defaultMethods',
          style: TextStyle(
            color: Colors.white.withOpacity(0.8),
            fontSize: 24.p,
          ),
        );
      }

      // 生成认证方式显示文字
      final methodsText = gateSuitableMethods
          .map((method) => GateAuthConfig.getAuthMethodDisplayName(method))
          .join('、');

      return Text(
        '支持$methodsText',
        style: TextStyle(
          color: Colors.white.withOpacity(0.8),
          fontSize: 24.p,
        ),
      );
    } catch (e) {
      // 出错时显示默认文字
      debugPrint('AuthContent: 获取认证方式失败: $e');
      return Text(
        '支持读者证、微信扫码、人脸识别',
        style: TextStyle(
          color: Colors.white.withOpacity(0.8),
          fontSize: 24.p,
        ),
      );
    }
  }
}

